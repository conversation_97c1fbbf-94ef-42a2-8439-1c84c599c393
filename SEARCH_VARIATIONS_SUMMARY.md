# Affinity Search Variations Summary

## Overview
The search functionality has been significantly reduced from the previous complex system that generated hundreds of variations to a much more targeted approach.

## Personal Profile Search Variations

### For 2 Names (First Last):
- **Example**: "<PERSON>"
- **Searches**: 1 variation
  - "<PERSON>"

### For 3 Names (First Middle Last):
- **Example**: "<PERSON>"
- **Searches**: 3 variations (if middle is full name)
  - "<PERSON>"
  - "<PERSON>" 
  - "<PERSON>"

- **Example**: "<PERSON>" or "<PERSON>"
- **Searches**: 1 variation (if middle is initial)
  - "<PERSON>"

### For 4+ Names:
- **Example**: "<PERSON>"
- **Searches**: 1 variation
  - "<PERSON>" (first + last only)

### For 1 Name:
- **Example**: "<PERSON>"
- **Searches**: 1 variation
  - "Madonna"

## Company Profile Search Variations

### Base Company Name:
- **Example**: "Blue Wire Capital"
- **Always includes**: Original name

### Word Replacements (bidirectional):
- incorporated ↔ inc
- corporation ↔ corp  
- limited ↔ ltd
- company ↔ co
- technologies ↔ tech
- and ↔ &

### Legal Suffix Removal:
- Removes: inc, incorporated, corp, corporation, llc, ltd, limited, lp, llp, plc
- Removes: holdings, group, solutions, technologies, systems, partners, ventures, capital

### "The" Prefix Handling:
- If starts with "The ": adds version without "The"
- If doesn't start with "The ": adds version with "The " prefix

### Example Company Variations:
**Input**: "Blue Wire Capital Technologies Inc"
**Variations**:
1. "Blue Wire Capital Technologies Inc" (original)
2. "Blue Wire Capital Tech Inc" (technologies → tech)
3. "Blue Wire Capital Technologies Incorporated" (inc → incorporated)
4. "Blue Wire Capital Tech Incorporated" (both replacements)
5. "Blue Wire Capital Technologies" (remove inc)
6. "Blue Wire Capital Tech" (tech + remove inc)
7. "Blue Wire Capital" (remove technologies inc)
8. "The Blue Wire Capital Technologies Inc" (add "The")
9. [Additional combinations with "The" prefix]

## Comparison: Old vs New System

### Old System (Removed):
- Generated 50-200+ search variations per name
- Complex separator swapping (space, dot, dash, underscore, comma)
- CamelCase detection and splitting
- Extensive word replacements and abbreviations
- Multiple case variations (lowercase, uppercase, capitalized)
- Legal suffix permutations
- Resulted in hundreds of API calls

### New System (Current):
- **Personal profiles**: 1-3 search variations maximum
- **Company profiles**: 5-15 search variations typically
- Focused on meaningful name combinations only
- Significantly reduced API calls
- Faster search performance
- More relevant results

## Benefits of Reduced Search:
1. **Performance**: Dramatically fewer API calls
2. **Relevance**: More targeted, meaningful results
3. **Speed**: Faster response times
4. **API Limits**: Reduced risk of hitting rate limits
5. **Accuracy**: Less noise in search results

## Search Logic Summary:
- **Personal**: Focus on actual name combinations people use
- **Company**: Focus on common business name variations
- **No more**: Excessive separator swapping, case variations, or speculative combinations
