# Affinity Search Variations Summary

## Overview
The search functionality has been significantly reduced from the previous complex system that generated hundreds of variations to a much more targeted approach.

## Personal Profile Search Variations

### For 2 Names (First Last):
- **Example**: "<PERSON>"
- **Searches**: 1 variation
  - "<PERSON>"

### For 3 Names (First Middle Last):
- **Example**: "<PERSON>"
- **Searches**: 3 variations (if middle is full name)
  - "<PERSON>"
  - "<PERSON>" 
  - "<PERSON>"

- **Example**: "<PERSON>" or "<PERSON>"
- **Searches**: 1 variation (if middle is initial)
  - "<PERSON>"

### For 4+ Names:
- **Example**: "<PERSON>"
- **Searches**: 1 variation
  - "<PERSON>" (first + last only)

### For 1 Name:
- **Example**: "<PERSON>"
- **Searches**: 1 variation
  - "Madonna"

## Company Profile Search Variations

### Strategy: Clean Base Name + Space/Dot Variations Only

### Step 1: Remove Less Specific Words
- Remove "The" prefix
- Remove legal suffixes: inc, incorporated, corp, corporation, llc, ltd, limited, lp, llp, plc, gmbh
- Remove business words: holdings, group, solutions, technologies, systems, partners, ventures, capital, company, co

### Step 2: Create Space/Dot Variations
- Original name
- Cleaned name (if different)
- Replace spaces with dots
- Replace dots with spaces

### Example Company Variations:
**Input**: "The Blue Wire Capital Technologies Inc"
**Variations**:
1. "The Blue Wire Capital Technologies Inc" (original)
2. "Blue Wire Capital" (cleaned: removed "The", "Technologies", "Inc")
3. "The.Blue.Wire.Capital.Technologies.Inc" (original with dots)
4. "Blue.Wire.Capital" (cleaned with dots)

**Input**: "Acme.Corp.Ltd"
**Variations**:
1. "Acme.Corp.Ltd" (original)
2. "Acme" (cleaned: removed "Corp", "Ltd")
3. "Acme Corp Ltd" (original with spaces)
4. "Acme" (cleaned with spaces - same as #2)

**Total**: Typically 2-4 variations maximum

## Comparison: Old vs New System

### Old System (Removed):
- Generated 50-200+ search variations per name
- Complex separator swapping (space, dot, dash, underscore, comma)
- CamelCase detection and splitting
- Extensive word replacements and abbreviations
- Multiple case variations (lowercase, uppercase, capitalized)
- Legal suffix permutations
- Resulted in hundreds of API calls

### New System (Current):
- **Personal profiles**: 1-3 search variations maximum
- **Company profiles**: 2-4 search variations maximum
- Focused on core name + space/dot variations only
- Dramatically reduced API calls
- Fastest search performance
- Most relevant results

## Benefits of Reduced Search:
1. **Performance**: Dramatically fewer API calls
2. **Relevance**: More targeted, meaningful results
3. **Speed**: Faster response times
4. **API Limits**: Reduced risk of hitting rate limits
5. **Accuracy**: Less noise in search results

## Search Logic Summary:
- **Personal**: Focus on actual name combinations people use
- **Company**: Focus on common business name variations
- **No more**: Excessive separator swapping, case variations, or speculative combinations
