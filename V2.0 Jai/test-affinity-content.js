// test-affinity-content.js
// This script can be used to test the affinity-content.js functionality

// Mock the chrome.runtime API
const chrome = {
  runtime: {
    sendMessage: (message, callback) => {
      console.log('Sending message:', message);
      if (message.type === 'getApiKey') {
        // Mock API key response
        callback({ apiKey: 'test-api-key' });
      }
    }
  }
};

// Mock fetch API
window.fetch = async (url, options) => {
  console.log('Fetch request:', url, options);
  
  // Mock responses based on the URL
  if (url.includes('/opportunities/')) {
    return {
      ok: true,
      json: () => Promise.resolve({
        id: 123,
        name: 'Test Opportunity',
        list_entries: [
          { id: 456, list_id: 789 }
        ]
      })
    };
  }
  
  if (url.includes('/notes?opportunity_id=')) {
    return {
      ok: true,
      json: () => Promise.resolve([
        {
          id: 1,
          content: 'This is a test note',
          created_at: new Date().toISOString()
        },
        {
          id: 2,
          content: 'This is another test note',
          created_at: new Date(Date.now() - 86400000).toISOString() // 1 day ago
        }
      ])
    };
  }
  
  if (url.includes('/lists/')) {
    return {
      ok: true,
      json: () => Promise.resolve({
        id: 789,
        name: 'Test List',
        fields: [
          {
            id: 101,
            name: 'Status',
            dropdown_options: [
              { id: 201, text: 'New' },
              { id: 202, text: 'In Progress' },
              { id: 203, text: 'Closed Won' },
              { id: 204, text: 'Closed Lost' }
            ]
          }
        ]
      })
    };
  }
  
  if (url.includes('/field-values?opportunity_id=')) {
    return {
      ok: true,
      json: () => Promise.resolve([
        {
          id: 301,
          value: 202 // In Progress
        }
      ])
    };
  }
  
  if (url.includes('/field-values') && options.method === 'POST') {
    return {
      ok: true,
      json: () => Promise.resolve({ success: true })
    };
  }
  
  if (url.includes('/notes') && options.method === 'POST') {
    return {
      ok: true,
      json: () => Promise.resolve({ id: 3, content: JSON.parse(options.body).content })
    };
  }
  
  return {
    ok: false,
    status: 404,
    statusText: 'Not Found'
  };
};

// Mock DOM elements
document.body.innerHTML = `
  <div class="list-view-container">
    <div class="list-entry-card" data-id="123">
      <a href="/opportunities/123">Test Opportunity</a>
    </div>
  </div>
`;

// Load the affinity-content.js script
console.log('Loading affinity-content.js...');

// You would need to include the affinity-content.js file here
// For testing purposes, you can copy the relevant functions here

// Test function to simulate clicking on an opportunity card
function testClickOpportunity() {
  console.log('Testing click on opportunity card...');
  const card = document.querySelector('.list-entry-card');
  if (card) {
    const event = new MouseEvent('click', {
      bubbles: true,
      cancelable: true
    });
    card.dispatchEvent(event);
  } else {
    console.error('No opportunity card found in the DOM');
  }
}

// Test function to simulate updating an opportunity
function testUpdateOpportunity() {
  console.log('Testing update opportunity...');
  // This would be called when the update button is clicked
  // You would need to implement this based on your handleUpdate function
}

// Run the tests
console.log('Running tests...');
// Uncomment these lines to run the tests
// testClickOpportunity();
// setTimeout(testUpdateOpportunity, 2000);

console.log('Test script loaded. You can run tests by calling testClickOpportunity() and testUpdateOpportunity() in the console.');
