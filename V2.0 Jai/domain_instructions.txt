# Domain Lookup Feature Implementation Instructions

## User Requirements

The user requested the following changes to the Chrome extension:

"there has been a new feature added to show registered domains under the person's name. i want this to instead only call the api when the user clicks a button that says 'find domains'. when they click it, it should bring up a little pop-up window that then calls the api and returns the list of companies under the person's name.

This button should be inverse colours to the others (blue text, white background, blue border and be right-aligned. you might need to make each of the buttons a smidge smaller to make it look right and good"

## Overview

The task involves:
1. Removing automatic domain loading that appears under the person's name
2. Adding a "Find Domains" button with specific styling (inverse colors, right-aligned)
3. Creating a popup that shows when the button is clicked
4. Making existing buttons slightly smaller to accommodate the new button
5. Implementing the domain search functionality in the popup

## Current State (Before Implementation)

The admin panel currently has:
- Automatic "Registered Domains" section that loads on page load
- Two buttons: "Add to Affinity" and "Outreach Template" (side by side, left-aligned)
- Automatic API call to search for domains when the page loads

## Target State (After Implementation)

The admin panel should have:
- No automatic domain loading
- Three buttons: "Add to Affinity", "Outreach Template" (left side), and "Find Domains" (right side)
- Popup functionality for domain search triggered by button click
- Slightly smaller button sizes to fit all three buttons

## Specific Code Changes Required

### 1. Remove Automatic Domain Section

Remove this HTML section from the admin panel:
```javascript
// Remove this code block:
if (!isCompanyPage) {
  html += `
    <div id="registered-domains-section" style="margin-top:16px;">
      <h3 style="margin:0 0 8px;color:#191919;font-size:16px;font-weight:600;">
        Registered Domains
      </h3>
      <div id="domains-content" style="color:#666;">
        <p id="domains-loading" style="margin:0;">Loading domain information...</p>
      </div>
    </div>
  `;
}
```

### 2. Remove Automatic API Call

Remove this code that triggers domain search on page load:
```javascript
// Remove this code block:
if (!isCompanyPage) {
  const name = getProfileName();
  if (name) {
    searchRegisteredDomains(name);
  }
}
```

### 3. Update Button Layout

Replace the existing button layout with this new structure:

```javascript
// Replace existing button HTML with:
<div style="display: flex; gap: 10px; margin-top: 16px; justify-content: space-between; align-items: center;">
  <div style="display: flex; gap: 10px;">
    <button
      id="add-to-affinity"
      style="
        padding:6px 16px;
        background-color:#0a66c2;
        color:#fff;
        border:none;
        border-radius:24px;
        cursor:pointer;
        font-size:15px;
        font-weight:600;
        transition: background-color 0.2s;
        width: auto;
        min-width: 120px;
      "
      onmouseover="this.style.backgroundColor='#004182'"
      onmouseout="this.style.backgroundColor='#0a66c2'"
    >
      Add to Affinity
    </button>
    
    <!-- Add Outreach Template button only for individual profiles -->
    <button
      id="outreach-template"
      style="
        padding:6px 16px;
        background-color:#0a66c2;
        color:#fff;
        border:none;
        border-radius:24px;
        cursor:pointer;
        font-size:15px;
        font-weight:600;
        transition: background-color 0.2s;
        width: auto;
        min-width: 120px;
      "
      onmouseover="this.style.backgroundColor='#004182'"
      onmouseout="this.style.backgroundColor='#0a66c2'"
    >
      Outreach Template
    </button>
  </div>
  
  <!-- Find Domains button (right-aligned, inverse colors) -->
  <button
    id="find-domains"
    style="
      padding:6px 16px;
      background-color:#fff;
      color:#0a66c2;
      border:1px solid #0a66c2;
      border-radius:24px;
      cursor:pointer;
      font-size:15px;
      font-weight:600;
      transition: all 0.2s;
      width: auto;
      min-width: 100px;
    "
    onmouseover="this.style.backgroundColor='#f8f9fa'"
    onmouseout="this.style.backgroundColor='#fff'"
  >
    Find Domains
  </button>
</div>
```

### 4. Add Button Reference

Add the new button reference:
```javascript
const findDomainsBtn = panel.querySelector('#find-domains');
```

### 5. Add Event Listener

Add this event listener for the Find Domains button:
```javascript
// Add event listener for Find Domains button
if (findDomainsBtn) {
  findDomainsBtn.addEventListener('click', async () => {
    console.log('[content.js] Find Domains button clicked!');
    findDomainsBtn.disabled = true;
    findDomainsBtn.textContent = 'Searching...';

    try {
      const name = getProfileName();
      if (name) {
        await showDomainsPopup(name);
      } else {
        throw new Error('Could not get profile name');
      }
      findDomainsBtn.textContent = 'Find Domains';
      findDomainsBtn.disabled = false;
    } catch (error) {
      console.error('[content.js] Error finding domains:', error);
      findDomainsBtn.textContent = 'Error ❌';
      setTimeout(() => {
        findDomainsBtn.textContent = 'Find Domains';
        findDomainsBtn.disabled = false;
      }, 2000);
    }
  });
} else {
  console.log('[content.js] Find Domains button not found in DOM');
}
```

## Key Implementation Notes

### Button Sizing Changes
- Reduced padding from `8px 20px` to `6px 16px`
- Reduced font size from `16px` to `15px`
- Reduced min-width from `140px` to `120px` (100px for Find Domains)

### Layout Structure
- Use flexbox with `justify-content: space-between`
- Left side: container with two buttons (Add to Affinity, Outreach Template)
- Right side: single Find Domains button
- Gap of 10px between elements

### Styling Requirements
- Find Domains button: inverse colors (blue text, white background, blue border)
- Hover effect: light gray background (`#f8f9fa`)
- All buttons maintain consistent border-radius (24px) and font-weight (600)

### Conditional Display
- Outreach Template and Find Domains buttons only show for individual profiles
- Use `!isCompanyPage` condition to control visibility

### Error Handling
- Button shows "Searching..." during API call
- Shows "Error ❌" if search fails
- Automatically resets after 2 seconds

## Files to Modify

1. **content.js** - Main implementation file
   - Remove automatic domain section and API call
   - Update button layout and styling
   - Add new event listener
   - Implement popup functionality

## Testing Considerations

1. Test on individual LinkedIn profiles (not company pages)
2. Verify button layout and sizing on different screen sizes
3. Test popup functionality and error handling
4. Ensure existing functionality (Add to Affinity, Outreach Template) still works
5. Verify domain search API integration works correctly

## Popup Implementation

### 6. Create Popup Function

Add this function to create and show the domains popup:

```javascript
/** Show domains popup and search for registered domains */
async function showDomainsPopup(name) {
  console.log('[content.js] Showing domains popup for:', name);

  // Create popup overlay
  const overlay = document.createElement('div');
  overlay.className = 'bwc-domains-popup-overlay';
  overlay.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
  `;

  // Create popup container
  const popup = document.createElement('div');
  popup.style.cssText = `
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    width: 500px;
    max-width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
  `;

  // Create header
  const header = document.createElement('div');
  header.style.cssText = `
    padding: 16px 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  `;

  const title = document.createElement('h2');
  title.textContent = 'Registered Domains';
  title.style.cssText = `
    font-size: 18px;
    font-weight: 600;
    margin: 0;
  `;

  const closeBtn = document.createElement('button');
  closeBtn.innerHTML = '&times;';
  closeBtn.style.cssText = `
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #666;
  `;
  closeBtn.addEventListener('click', () => overlay.remove());

  header.appendChild(title);
  header.appendChild(closeBtn);

  // Create content area
  const content = document.createElement('div');
  content.style.cssText = `
    padding: 20px;
  `;

  // Add loading message
  const loadingText = document.createElement('p');
  loadingText.textContent = 'Searching for registered domains...';
  loadingText.style.cssText = `
    margin: 0;
    color: #666;
    text-align: center;
  `;

  content.appendChild(loadingText);
  popup.appendChild(header);
  popup.appendChild(content);
  overlay.appendChild(popup);
  document.body.appendChild(overlay);

  // Close popup when clicking outside
  overlay.addEventListener('click', (e) => {
    if (e.target === overlay) {
      overlay.remove();
    }
  });

  // Close popup with Escape key
  const handleEscapeKey = (e) => {
    if (e.key === 'Escape') {
      overlay.remove();
      document.removeEventListener('keydown', handleEscapeKey);
    }
  };
  document.addEventListener('keydown', handleEscapeKey);

  // Perform the domain search
  try {
    const response = await new Promise(resolve =>
      chrome.runtime.sendMessage({ type: 'reverseWhoisLookup', name }, resolve)
    );

    console.log('[content.js] Reverse WHOIS response:', response);

    if (response && response.success) {
      displayDomainsInPopup(content, response.domains, response.totalFound);
    } else {
      displayDomainsErrorInPopup(content, response?.error || 'Failed to search for registered domains');
    }
  } catch (error) {
    console.error('[content.js] Error during reverse WHOIS lookup:', error);
    displayDomainsErrorInPopup(content, 'Error searching for registered domains');
  }
}
```

### 7. Add Helper Functions

Add these helper functions for displaying results:

```javascript
/** Display domains in popup */
function displayDomainsInPopup(content, domains, totalFound) {
  // Clear loading message
  content.innerHTML = '';

  if (!domains || domains.length === 0) {
    content.innerHTML = '<p style="margin:0;color:#666;text-align:center;">No registered domains found.</p>';
    return;
  }

  // Create results summary
  const summary = document.createElement('p');
  if (totalFound === 1) {
    summary.textContent = '1 registered domain found:';
  } else {
    summary.textContent = `${totalFound} registered domains found:`;
  }
  summary.style.cssText = `
    margin: 0 0 16px 0;
    color: #666;
    font-weight: 500;
  `;
  content.appendChild(summary);

  // Create domains list
  const domainsList = document.createElement('ul');
  domainsList.style.cssText = `
    margin: 0;
    padding: 0;
    list-style: none;
    max-height: 400px;
    overflow-y: auto;
  `;

  domains.forEach(domain => {
    const listItem = document.createElement('li');
    listItem.style.cssText = `
      margin-bottom: 8px;
      padding: 8px 12px;
      background-color: #f8f9fa;
      border-radius: 4px;
      border: 1px solid #e9ecef;
    `;

    const link = document.createElement('a');
    link.href = `http://${domain}`;
    link.target = '_blank';
    link.textContent = domain;
    link.style.cssText = `
      color: #0a66c2;
      text-decoration: none;
      font-weight: 500;
    `;

    listItem.appendChild(link);
    domainsList.appendChild(listItem);
  });

  content.appendChild(domainsList);
}

/** Display error in popup */
function displayDomainsErrorInPopup(content, errorMessage) {
  content.innerHTML = `
    <p style="margin:0;color:#666;text-align:center;">
      ${errorMessage}
    </p>
  `;
}
```

## API Integration

The domain search functionality should use the existing `reverseWhoisLookup` message type to communicate with the background script. The popup should display results in a clean, scrollable format with clickable domain links.

## Cleanup Tasks

After implementing the new popup functionality, remove these old functions that are no longer needed:
- `displayRegisteredDomains()`
- `displayDomainsError()`
- `showAllDomains()` (if it exists)

## Final Verification

1. Ensure no automatic domain loading occurs
2. Verify three buttons are properly positioned and sized
3. Test popup functionality with real domain searches
4. Confirm all existing features still work correctly
5. Test on both individual profiles and company pages
