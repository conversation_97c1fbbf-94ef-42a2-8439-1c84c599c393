// fetch-owners.js
// <PERSON><PERSON><PERSON> to fetch all owners from Affinity API

// ───────────────────────────────────────────────────────────
// API configuration (using the same key from background.js)
const affinityApiKey = "cnGLFJ4cArdpLUQ0ihf_c2hIn-XNN7R7AUypiMZo-bA";
const BASE_URL = "https://api.affinity.co";
const HEADERS = {
  "Authorization": "Basic " + btoa(":" + affinityApiKey),
  "Content-Type": "application/json"
};
// ───────────────────────────────────────────────────────────

// Main function to fetch owners
async function fetchOwners() {
  try {
    console.log("Fetching owners from Affinity API...");
    
    // 1. First, we need to get the list metadata to find the owners field ID
    // Using list ID 85510 as seen in the codebase
    const listRes = await fetch(`${BASE_URL}/lists/85510`, { headers: HEADERS });
    
    if (!listRes.ok) {
      throw new Error(`List metadata GET failed: ${listRes.status}`);
    }
    
    const listJson = await listRes.json();
    console.log('List fields:', listJson.fields);
    
    // Find the owners field
    const ownersField = listJson.fields.find(f => f.name.toLowerCase().includes('owner'));
    
    if (!ownersField) {
      throw new Error('Owners field not found');
    }
    
    console.log('Owners field ID:', ownersField.id);
    
    // 2. Now we need to fetch all persons who can be owners
    // The API doesn't have a direct endpoint to get all possible owners,
    // but we can use the /persons endpoint to get all persons
    
    // First, let's try to get all persons
    const personsRes = await fetch(`${BASE_URL}/persons`, { headers: HEADERS });
    
    if (!personsRes.ok) {
      console.log(`Persons GET failed: ${personsRes.status}. Trying alternative approach...`);
      
      // If we can't get all persons, we'll try to search for common domains
      // This is a fallback approach
      const domains = ['bluewirecapital.com', 'affinity.co'];
      const personsPromises = domains.map(domain => 
        fetch(`${BASE_URL}/persons?term=${encodeURIComponent(domain)}`, { headers: HEADERS })
          .then(res => res.ok ? res.json() : { persons: [] })
      );
      
      const personsResults = await Promise.all(personsPromises);
      
      // Combine and deduplicate persons
      const allPersons = [];
      const seenIds = new Set();
      
      personsResults.forEach(result => {
        if (result.persons) {
          result.persons.forEach(person => {
            if (!seenIds.has(person.id)) {
              seenIds.add(person.id);
              allPersons.push(person);
            }
          });
        }
      });
      
      // Display the results
      console.log(`Found ${allPersons.length} potential owners:`);
      console.log('Owners field ID:', ownersField.id);
      console.log('-----------------------------------');
      
      allPersons.forEach(person => {
        console.log(`ID: ${person.id}`);
        console.log(`Name: ${person.first_name} ${person.last_name}`);
        console.log(`Emails: ${person.emails ? person.emails.join(', ') : 'No email'}`);
        console.log('-----------------------------------');
      });
      
      return;
    }
    
    // If we successfully got all persons
    const personsJson = await personsRes.json();
    const persons = personsJson.persons || [];
    
    console.log(`Found ${persons.length} potential owners:`);
    console.log('Owners field ID:', ownersField.id);
    console.log('-----------------------------------');
    
    persons.forEach(person => {
      console.log(`ID: ${person.id}`);
      console.log(`Name: ${person.first_name} ${person.last_name}`);
      console.log(`Emails: ${person.emails ? person.emails.join(', ') : 'No email'}`);
      console.log('-----------------------------------');
    });
    
  } catch (error) {
    console.error('Error fetching owners:', error);
  }
}

// Execute the function
fetchOwners();
