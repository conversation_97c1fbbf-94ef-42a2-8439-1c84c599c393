# Unified Chrome Extension with Config-Based User Toggles

## Overview

This unified system consolidates the 3 Chrome extension versions (<PERSON>, <PERSON>, <PERSON>) into a single codebase with config-based feature toggles. Each user gets their own folder with a customized `config.json` file that enables/disables features according to their needs.

## Folder Structure

```
base-version/          # Base version with all features (for development)
bertie-version/        # <PERSON>'s customized version
hanna-version/         # <PERSON>'s customized version  
jai-version/           # <PERSON>'s customized version
```

## Features Available

### 1. Add to Affinity
- **Description**: Adds LinkedIn profiles to Affinity CRM
- **Available in**: All versions
- **Button Label**: 
  - Bertie: "Add to New"
  - <PERSON> <PERSON> <PERSON>: "Add to Affinity"

### 2. Add to Reach Out
- **Description**: Adds LinkedIn profiles to Affinity with "To Reach Out" status
- **Available in**: Bertie only
- **Button Label**: "Add to Reach Out"

### 3. Find Domains
- **Description**: Searches for registered domains associated with the profile name
- **Available in**: All versions
- **Button Label**: "Find Domains"

### 4. Outreach Template
- **Description**: Generates and copies personalized outreach messages to clipboard
- **Available in**: <PERSON> and <PERSON> only
- **Button Label**: "Outreach Template"
- **Templates**:
  - Hanna: "Hey {firstName} - would love to learn more about what you're building. I'm an early-stage, global investor with Blue Wire - we're a very founder-focussed fund (first checks into Monzo & Deliveroo) & like that "
  - Jai: "Hi {firstName},\n\nI work at Blue Wire Capital, a VC fund in London (previous investments include Monzo, Deliveroo, and beehiiv)."

### 5. Add to Talent Bank
- **Description**: Adds LinkedIn profiles to Airtable talent bank with detailed categorization
- **Available in**: All versions
- **Button Label**: "Add to Talent Bank"
- **Data Collected**:
  - Name (automatically extracted from LinkedIn profile)
  - About section (automatically extracted from LinkedIn profile with improved detection)
  - Profile URL (current page URL)
  - Functions (multi-select checkboxes): Technical, Product, Sales/Commercial, Marketing, Operations, Finance, People/HR, Legal/Compliance
  - Purpose (multi-select checkboxes): Potential hire, Potential future founder, Useful for info
  - When to reach out again (optional - requires checkbox to enable date picker)
  - Specific portcos they might be of interest to (optional text field)
  - BWC Contact (automatically set based on user: Bertie, Hanna, or Jai)

## Configuration System

Each version contains a `config.json` file that controls feature availability:

### Bertie's Configuration
```json
{
  "user": "bertie",
  "features": {
    "addToAffinity": true,
    "addToReachOut": true,
    "findDomains": true,
    "outreachTemplate": false,
    "addToTalentBank": true
  },
  "settings": {
    "affinityButtonLabel": "Add to New",
    "outreachMessage": "template_version_1",
    "bwcContactName": "Bertie"
  }
}
```

### Hanna's Configuration
```json
{
  "user": "hanna",
  "features": {
    "addToAffinity": true,
    "addToReachOut": false,
    "findDomains": true,
    "outreachTemplate": true,
    "addToTalentBank": true
  },
  "settings": {
    "affinityButtonLabel": "Add to Affinity",
    "outreachMessage": "template_version_1",
    "bwcContactName": "Hanna"
  }
}
```

### Jai's Configuration
```json
{
  "user": "jai",
  "features": {
    "addToAffinity": true,
    "addToReachOut": false,
    "findDomains": true,
    "outreachTemplate": true,
    "addToTalentBank": true
  },
  "settings": {
    "affinityButtonLabel": "Add to Affinity",
    "outreachMessage": "template_version_2",
    "bwcContactName": "Jai"
  }
}
```

## How It Works

1. **Config Loading**: When the extension loads, it fetches the `config.json` file using `chrome.runtime.getURL("config.json")`

2. **Feature Rendering**: The UI dynamically renders buttons based on the config:
   - Only enabled features show their buttons
   - Button labels are customized per user
   - Outreach templates vary by user

3. **Runtime Behavior**: All functionality is conditionally executed based on the loaded configuration

## Installation Instructions

1. Choose the appropriate version folder for your user (bertie-version, hanna-version, or jai-version)
2. Load the folder as an unpacked extension in Chrome
3. The extension will automatically load the user-specific configuration

**Note**: Airtable integration is already configured and ready to use for the Talent Bank feature.

## Airtable Setup

For the Talent Bank feature to work, you need to set up an Airtable base with the following structure:

**Table Name**: "Talent Bank" (or update the name in background.js)

**Required Fields**:
- Name (Single line text)
- About (Long text)
- Profile Link (URL)
- Functions (Multiple select): Technical, Product, Sales/Commercial, Marketing, Operations, Finance, People/HR, Legal/Compliance
- Purpose (Multiple select): Potential hire, Potential future founder, Useful for info
- BWC Contact (Single line text)
- When to reach out again (Date)
- Specific portcos they might be of interest to (Single line text)

## Development Process

### Adding New Features

When adding a new feature to the extension, follow this process:

1. **Develop in Base Version First**
   - Always implement new features in `base-version/` first
   - Test thoroughly in the base version before deploying to user versions
   - Ensure the feature works with all possible config combinations

2. **Make Features Config-Driven**
   - Add a new feature toggle to the config schema (e.g., `"newFeature": true/false`)
   - Use conditional rendering based on `extensionConfig?.features?.newFeature`
   - Add any feature-specific settings to the `settings` section of config

3. **Update Config Schema**
   - Add the new feature flag to `base-version/config.json` with `true` (to test all features)
   - Document what the feature does and any settings it requires
   - Consider if the feature needs user-specific customization (like button labels, API endpoints, etc.)

4. **Implement Conditional Logic**
   - Wrap UI elements in config checks: `if (extensionConfig?.features?.newFeature) { ... }`
   - Wrap event listeners in config checks: `if (newFeatureBtn) { ... }`
   - Ensure graceful degradation when features are disabled

5. **Update User Configs**
   - Decide which users should get the new feature
   - Update each user's `config.json` file accordingly
   - Consider if any users need feature-specific settings

6. **Test Each User Version**
   - Load each user version as an unpacked extension
   - Verify that enabled features work correctly
   - Verify that disabled features don't appear in the UI
   - Test edge cases and error handling

### Config Schema Guidelines

```json
{
  "user": "username",
  "features": {
    "existingFeature": true,
    "newFeature": false,           // Add new feature flags here
    "anotherFeature": true
  },
  "settings": {
    "existingButtonLabel": "Label",
    "newFeatureApiEndpoint": "...", // Add feature-specific settings here
    "newFeatureTemplate": "..."
  }
}
```

### Code Patterns to Follow

**Feature Detection:**
```javascript
// Check if feature is enabled before rendering UI
if (extensionConfig?.features?.newFeature) {
  html += `<button id="new-feature-btn">New Feature</button>`;
}
```

**Event Listener Setup:**
```javascript
// Always check if element exists before adding listeners
const newFeatureBtn = panel.querySelector('#new-feature-btn');
if (newFeatureBtn) {
  newFeatureBtn.addEventListener('click', handleNewFeature);
}
```

**Settings Usage:**
```javascript
// Use settings with fallbacks
const apiEndpoint = extensionConfig?.settings?.newFeatureApiEndpoint || 'default-endpoint';
const template = extensionConfig?.settings?.newFeatureTemplate || 'default-template';
```

### Development Notes

- **Base Version**: Use `base-version/` for development and testing all features
- **Shared Code**: All core functionality is shared across versions
- **Config-Driven**: No code changes needed to enable/disable features for users
- **Fallback**: If config loading fails, the extension falls back to a safe default configuration

## Files Modified

- `manifest.json`: Added `web_accessible_resources` for config.json
- `content.js`: Added config loading and conditional feature rendering
- `config.json`: New file controlling feature toggles per user

### Deployment Process

1. **Test in Base Version**
   - Load `base-version/` as unpacked extension
   - Test all features work correctly
   - Verify config loading and fallback behavior

2. **Update User Versions**
   - Copy changes from `base-version/` to user versions (content.js, background.js, manifest.json, etc.)
   - Update each user's `config.json` as needed
   - Do NOT copy `config.json` from base-version to user versions

3. **Test Each User Version**
   - Load each user version separately
   - Verify only enabled features appear
   - Test all enabled functionality works

4. **Document Changes**
   - Update this README if new config options are added
   - Document any breaking changes or migration steps

### Troubleshooting

**Config Not Loading:**
- Check browser console for fetch errors
- Verify `config.json` is valid JSON
- Ensure `web_accessible_resources` includes `config.json` in manifest

**Features Not Appearing:**
- Check config loading in browser console
- Verify feature flags are set to `true` in user's config
- Check for JavaScript errors in console

**Features Not Working:**
- Verify all required settings are present in config
- Check that background.js supports all required message types
- Test in base-version first to isolate config issues

## Benefits

1. **Single Codebase**: Easier maintenance and updates
2. **User Customization**: Each user gets exactly the features they need
3. **Easy Configuration**: Simple JSON file controls all behavior
4. **No Code Duplication**: Shared functionality across all versions
5. **Future Flexibility**: Easy to add new features or users
6. **Development Efficiency**: Clear process for adding features
7. **Testing Isolation**: Test in base-version before user deployment
