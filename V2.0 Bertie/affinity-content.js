// affinity-content.js
// This script runs on Affinity pages and adds functionality to the deal board

// Global variables
const API_BASE_URL = "https://api.affinity.co";
let affinityApiKey = null;

// Get the API key from the background script
chrome.runtime.sendMessage({ type: 'getApiKey' }, (response) => {
  if (response && response.apiKey) {
    affinityApiKey = response.apiKey;
    initAffinityExtension();
  } else {
    console.error('[affinity-content.js] Failed to get API key');
  }
});

// Initialize the extension
function initAffinityExtension() {
  // Check if we're on a page that contains the deal board
  if (document.querySelector('.list-view-container')) {
    console.log('[affinity-content.js] Deal board detected');
    setupDealBoardListeners();
  }

  // Listen for custom events from other content scripts
  document.addEventListener('bwc-show-opportunity', (event) => {
    if (event.detail && event.detail.opportunityId) {
      console.log('[affinity-content.js] Received request to show opportunity:', event.detail.opportunityId);
      handleOpportunityRequest(event.detail.opportunityId);
    }
  });
}

// Handle opportunity request from other content scripts
async function handleOpportunityRequest(opportunityId) {
  try {
    const opportunityDetails = await fetchOpportunityDetails(opportunityId);
    // Add the Affinity URL to the details
    opportunityDetails.affinityUrl = `https://bluewirecapital.affinity.co/opportunities/${opportunityId}`;
    showOpportunityPopup(opportunityDetails);
  } catch (error) {
    console.error('[affinity-content.js] Error fetching opportunity details:', error);
  }
}

// Set up event listeners for the deal board
function setupDealBoardListeners() {
  // Add CSS for our custom elements
  addCustomStyles();

  // Add click listeners to opportunity cards
  setupOpportunityCardListeners();

  // Watch for DOM changes (for SPA navigation and dynamic content loading)
  observeDOMChanges();
}

// Add custom styles for our popup and other UI elements
function addCustomStyles() {
  const styleEl = document.createElement('style');
  styleEl.textContent = `
    .bwc-popup-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 9999;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .bwc-popup {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      width: 500px;
      max-width: 90%;
      max-height: 90vh;
      overflow-y: auto;
      position: relative;
    }

    .bwc-popup-header {
      padding: 16px 20px;
      border-bottom: 1px solid #e0e0e0;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .bwc-popup-title {
      font-size: 18px;
      font-weight: 600;
      margin: 0;
    }

    .bwc-popup-close {
      background: none;
      border: none;
      font-size: 20px;
      cursor: pointer;
      color: #666;
    }

    .bwc-popup-content {
      padding: 20px;
    }

    .bwc-field-group {
      margin-bottom: 16px;
    }

    .bwc-field-label {
      font-weight: 600;
      margin-bottom: 8px;
      display: block;
    }

    .bwc-status-select {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      font-size: 14px;
      margin-bottom: 16px;
    }

    .bwc-notes-container {
      margin-bottom: 16px;
    }

    .bwc-note {
      background-color: #f5f5f5;
      border-radius: 4px;
      padding: 12px;
      margin-bottom: 8px;
    }

    .bwc-note-date {
      font-size: 12px;
      color: #666;
      margin-bottom: 4px;
    }

    .bwc-note-content {
      white-space: pre-wrap;
    }

    .bwc-new-note {
      width: 100%;
      min-height: 80px;
      padding: 12px;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      font-size: 14px;
      resize: vertical;
    }

    .bwc-popup-footer {
      padding: 16px 20px;
      border-top: 1px solid #e0e0e0;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .bwc-footer-right {
      display: flex;
      align-items: center;
    }

    .bwc-update-btn {
      background-color: #0a66c2;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 8px 16px;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
    }

    .bwc-update-btn:hover {
      background-color: #004182;
    }

    .bwc-view-btn {
      background-color: #ffffff;
      color: #0a66c2;
      border: 1px solid #0a66c2;
      border-radius: 4px;
      padding: 8px 16px;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
    }

    .bwc-view-btn:hover {
      background-color: rgba(10, 102, 194, 0.1);
    }

    .bwc-view-btn-icon {
      margin-right: 8px;
      width: 16px;
      height: 16px;
    }

    .bwc-status-message {
      margin-right: 12px;
      font-size: 14px;
      color: #666;
      align-self: center;
    }
  `;
  document.head.appendChild(styleEl);
}

// Set up click listeners for opportunity cards
function setupOpportunityCardListeners() {
  // Find all opportunity cards in the deal board
  const opportunityCards = document.querySelectorAll('.list-entry-card');

  opportunityCards.forEach(card => {
    // Remove any existing listeners
    card.removeEventListener('click', handleOpportunityCardClick);

    // Find all links within the card
    const links = card.querySelectorAll('a');
    links.forEach(link => {
      link.removeEventListener('click', handleOpportunityCardClick);
      link.addEventListener('click', handleOpportunityCardClick);
    });

    // Add new click listener to the card itself
    card.addEventListener('click', handleOpportunityCardClick);
  });
}

// Handle click on an opportunity card
async function handleOpportunityCardClick(event) {
  // Always prevent default behavior to stop navigation
  event.preventDefault();
  event.stopPropagation();

  // Get the opportunity ID from the card
  const opportunityId = getOpportunityIdFromCard(event.currentTarget);

  if (!opportunityId) {
    console.error('[affinity-content.js] Could not determine opportunity ID');
    return;
  }

  // Store the original URL for the "View in Affinity" button
  const affinityUrl = event.currentTarget.querySelector('a')?.href ||
                      `https://app.affinity.co/opportunities/${opportunityId}`;

  // Fetch opportunity details
  try {
    const opportunityDetails = await fetchOpportunityDetails(opportunityId);
    // Add the Affinity URL to the details
    opportunityDetails.affinityUrl = affinityUrl;
    showOpportunityPopup(opportunityDetails);
  } catch (error) {
    console.error('[affinity-content.js] Error fetching opportunity details:', error);
  }
}

// Extract opportunity ID from a card element
function getOpportunityIdFromCard(cardElement) {
  // Try to find the ID in the card's data attributes or URL
  const linkElement = cardElement.querySelector('a[href*="/opportunities/"]');

  if (linkElement) {
    const href = linkElement.getAttribute('href');
    const match = href.match(/\/opportunities\/(\d+)/);
    if (match && match[1]) {
      return match[1];
    }
  }

  // If we couldn't find it in the link, try other methods
  // This is a fallback and might need to be adjusted based on the actual DOM structure
  const idAttribute = cardElement.getAttribute('data-id') ||
                      cardElement.getAttribute('data-opportunity-id');

  return idAttribute;
}

// Fetch opportunity details from the Affinity API
async function fetchOpportunityDetails(opportunityId) {
  // Fetch the opportunity data
  const opportunityResponse = await fetch(`${API_BASE_URL}/opportunities/${opportunityId}`, {
    headers: {
      "Authorization": "Basic " + btoa(":" + affinityApiKey),
      "Content-Type": "application/json"
    }
  });

  if (!opportunityResponse.ok) {
    throw new Error(`Failed to fetch opportunity: ${opportunityResponse.status}`);
  }

  const opportunity = await opportunityResponse.json();

  // Fetch notes for this opportunity
  const notesResponse = await fetch(`${API_BASE_URL}/notes?opportunity_id=${opportunityId}`, {
    headers: {
      "Authorization": "Basic " + btoa(":" + affinityApiKey),
      "Content-Type": "application/json"
    }
  });

  if (!notesResponse.ok) {
    throw new Error(`Failed to fetch notes: ${notesResponse.status}`);
  }

  const notes = await notesResponse.json();

  // Fetch list entry to get field values (status)
  const listEntryId = opportunity.list_entries?.[0]?.id;
  let status = null;
  let statusField = null;
  let statusOptions = [];

  if (listEntryId) {
    // Get the list ID
    const listId = opportunity.list_entries[0].list_id;

    // Fetch list metadata to get the status field
    const listResponse = await fetch(`${API_BASE_URL}/lists/${listId}`, {
      headers: {
        "Authorization": "Basic " + btoa(":" + affinityApiKey),
        "Content-Type": "application/json"
      }
    });

    if (listResponse.ok) {
      const listData = await listResponse.json();
      statusField = listData.fields.find(f => f.name === 'Status');

      if (statusField) {
        statusOptions = statusField.dropdown_options || [];

        // Fetch the current status value
        const fieldValuesResponse = await fetch(
          `${API_BASE_URL}/field-values?opportunity_id=${opportunityId}&field_id=${statusField.id}`, {
          headers: {
            "Authorization": "Basic " + btoa(":" + affinityApiKey),
            "Content-Type": "application/json"
          }
        });

        if (fieldValuesResponse.ok) {
          const fieldValues = await fieldValuesResponse.json();
          if (fieldValues.length > 0) {
            const statusValue = fieldValues[0].value;
            status = statusOptions.find(opt => opt.id === statusValue) || null;
          }
        }
      }
    }
  }

  return {
    opportunity,
    notes,
    status,
    statusField,
    statusOptions,
    listEntryId
  };
}

// Show the opportunity popup with details
function showOpportunityPopup(details) {
  // Remove any existing popup
  removePopup();

  const { opportunity, notes, status, statusOptions, statusField, listEntryId } = details;

  // Create popup elements
  const overlay = document.createElement('div');
  overlay.className = 'bwc-popup-overlay';

  const popup = document.createElement('div');
  popup.className = 'bwc-popup';

  // Create header
  const header = document.createElement('div');
  header.className = 'bwc-popup-header';

  const title = document.createElement('h2');
  title.className = 'bwc-popup-title';
  title.textContent = opportunity.name || 'Opportunity Details';

  const closeBtn = document.createElement('button');
  closeBtn.className = 'bwc-popup-close';
  closeBtn.innerHTML = '&times;';
  closeBtn.addEventListener('click', removePopup);

  header.appendChild(title);
  header.appendChild(closeBtn);

  // Create content
  const content = document.createElement('div');
  content.className = 'bwc-popup-content';

  // Status dropdown
  if (statusField && statusOptions.length > 0) {
    const statusGroup = document.createElement('div');
    statusGroup.className = 'bwc-field-group';

    const statusLabel = document.createElement('label');
    statusLabel.className = 'bwc-field-label';
    statusLabel.textContent = 'Status:';
    statusLabel.setAttribute('for', 'bwc-status-select');

    const statusSelect = document.createElement('select');
    statusSelect.className = 'bwc-status-select';
    statusSelect.id = 'bwc-status-select';

    statusOptions.forEach(option => {
      const optEl = document.createElement('option');
      optEl.value = option.id;
      optEl.textContent = option.text;
      if (status && status.id === option.id) {
        optEl.selected = true;
      }
      statusSelect.appendChild(optEl);
    });

    statusGroup.appendChild(statusLabel);
    statusGroup.appendChild(statusSelect);
    content.appendChild(statusGroup);
  }

  // Notes section
  const notesGroup = document.createElement('div');
  notesGroup.className = 'bwc-field-group';

  const notesLabel = document.createElement('label');
  notesLabel.className = 'bwc-field-label';
  notesLabel.textContent = 'Notes:';

  const notesContainer = document.createElement('div');
  notesContainer.className = 'bwc-notes-container';

  // Sort notes by created_at in descending order (newest first)
  const sortedNotes = [...notes].sort((a, b) => {
    return new Date(b.created_at) - new Date(a.created_at);
  });

  // Display existing notes
  sortedNotes.forEach(note => {
    if (note.content) {
      const noteEl = document.createElement('div');
      noteEl.className = 'bwc-note';

      const noteDate = document.createElement('div');
      noteDate.className = 'bwc-note-date';
      const date = new Date(note.created_at);
      noteDate.textContent = date.toLocaleString();

      const noteContent = document.createElement('div');
      noteContent.className = 'bwc-note-content';
      noteContent.textContent = note.content;

      noteEl.appendChild(noteDate);
      noteEl.appendChild(noteContent);
      notesContainer.appendChild(noteEl);
    }
  });

  // New note textarea
  const newNoteLabel = document.createElement('label');
  newNoteLabel.className = 'bwc-field-label';
  newNoteLabel.textContent = 'Add a note:';
  newNoteLabel.setAttribute('for', 'bwc-new-note');

  const newNoteTextarea = document.createElement('textarea');
  newNoteTextarea.className = 'bwc-new-note';
  newNoteTextarea.id = 'bwc-new-note';
  newNoteTextarea.placeholder = 'Type your note here...';

  notesGroup.appendChild(notesLabel);
  notesGroup.appendChild(notesContainer);
  notesGroup.appendChild(newNoteLabel);
  notesGroup.appendChild(newNoteTextarea);

  content.appendChild(notesGroup);

  // Create footer with buttons
  const footer = document.createElement('div');
  footer.className = 'bwc-popup-footer';

  // Create "View in Affinity" button
  const viewBtn = document.createElement('a');
  viewBtn.className = 'bwc-view-btn';
  viewBtn.href = details.affinityUrl;
  viewBtn.target = '_blank';
  viewBtn.rel = 'noopener noreferrer';

  // Add Affinity icon (simplified version)
  const iconSvg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
  iconSvg.setAttribute('class', 'bwc-view-btn-icon');
  iconSvg.setAttribute('viewBox', '0 0 24 24');
  iconSvg.setAttribute('fill', '#0a66c2');

  const iconPath = document.createElementNS('http://www.w3.org/2000/svg', 'path');
  iconPath.setAttribute('d', 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z');

  iconSvg.appendChild(iconPath);
  viewBtn.appendChild(iconSvg);

  const viewBtnText = document.createTextNode('View in Affinity');
  viewBtn.appendChild(viewBtnText);

  // Create right side container for status message and update button
  const footerRight = document.createElement('div');
  footerRight.className = 'bwc-footer-right';

  const statusMessage = document.createElement('div');
  statusMessage.className = 'bwc-status-message';
  statusMessage.id = 'bwc-status-message';

  const updateBtn = document.createElement('button');
  updateBtn.className = 'bwc-update-btn';
  updateBtn.textContent = 'Update';
  updateBtn.addEventListener('click', () => handleUpdate(
    opportunity.id,
    listEntryId,
    statusField?.id,
    statusSelect?.value,
    status?.id,
    newNoteTextarea.value
  ));

  footerRight.appendChild(statusMessage);
  footerRight.appendChild(updateBtn);

  footer.appendChild(viewBtn);
  footer.appendChild(footerRight);

  // Assemble popup
  popup.appendChild(header);
  popup.appendChild(content);
  popup.appendChild(footer);
  overlay.appendChild(popup);

  // Add to document
  document.body.appendChild(overlay);

  // Close popup when clicking outside
  overlay.addEventListener('click', (e) => {
    if (e.target === overlay) {
      removePopup();
    }
  });

  // Close popup with Escape key
  document.addEventListener('keydown', handleEscapeKey);
}

// Remove the popup
function removePopup() {
  const overlay = document.querySelector('.bwc-popup-overlay');
  if (overlay) {
    overlay.remove();
    document.removeEventListener('keydown', handleEscapeKey);
  }
}

// Handle Escape key press
function handleEscapeKey(e) {
  if (e.key === 'Escape') {
    removePopup();
  }
}

// Handle update button click
async function handleUpdate(opportunityId, listEntryId, statusFieldId, newStatusId, oldStatusId, newNote) {
  const statusMessage = document.getElementById('bwc-status-message');
  const updateBtn = document.querySelector('.bwc-update-btn');

  // Check if anything has changed
  const statusChanged = newStatusId && oldStatusId !== newStatusId;
  const hasNewNote = newNote.trim().length > 0;

  if (!statusChanged && !hasNewNote) {
    statusMessage.textContent = 'No changes have been made';
    return;
  }

  // Disable button and show loading state
  updateBtn.disabled = true;
  updateBtn.textContent = 'Updating...';
  statusMessage.textContent = '';

  try {
    // Update status if changed
    if (statusChanged && statusFieldId && listEntryId) {
      const statusResponse = await fetch(`${API_BASE_URL}/field-values`, {
        method: 'POST',
        headers: {
          "Authorization": "Basic " + btoa(":" + affinityApiKey),
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          field_id: statusFieldId,
          entity_id: opportunityId,
          list_entry_id: listEntryId,
          value: newStatusId
        })
      });

      if (!statusResponse.ok) {
        throw new Error(`Failed to update status: ${statusResponse.status}`);
      }
    }

    // Add new note if provided
    if (hasNewNote) {
      const noteResponse = await fetch(`${API_BASE_URL}/notes`, {
        method: 'POST',
        headers: {
          "Authorization": "Basic " + btoa(":" + affinityApiKey),
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          content: newNote,
          opportunity_ids: [opportunityId],
          type: 2 // Regular note type
        })
      });

      if (!noteResponse.ok) {
        throw new Error(`Failed to add note: ${noteResponse.status}`);
      }
    }

    // Show success message
    statusMessage.textContent = 'Updated successfully!';
    updateBtn.textContent = 'Update';
    updateBtn.disabled = false;

    // Refresh the popup after a short delay
    setTimeout(async () => {
      try {
        const updatedDetails = await fetchOpportunityDetails(opportunityId);
        showOpportunityPopup(updatedDetails);
      } catch (error) {
        console.error('[affinity-content.js] Error refreshing opportunity details:', error);
      }
    }, 1000);

  } catch (error) {
    console.error('[affinity-content.js] Update error:', error);
    statusMessage.textContent = `Error: ${error.message}`;
    updateBtn.textContent = 'Update';
    updateBtn.disabled = false;
  }
}

// Observe DOM changes to handle dynamically loaded content
function observeDOMChanges() {
  const observer = new MutationObserver((mutations) => {
    // Check if any new opportunity cards were added
    const hasNewCards = mutations.some(mutation => {
      return Array.from(mutation.addedNodes).some(node => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          return node.classList?.contains('list-entry-card') ||
                 node.querySelector?.('.list-entry-card');
        }
        return false;
      });
    });

    if (hasNewCards) {
      setupOpportunityCardListeners();
    }
  });

  // Start observing the deal board container
  const dealBoardContainer = document.querySelector('.list-view-container');
  if (dealBoardContainer) {
    observer.observe(dealBoardContainer, {
      childList: true,
      subtree: true
    });
  }
}
