// background.js

// ───────────────────────────────────────────────────────────
// 1) Hard‐coded API keys (local use only)
const affinityApiKey = "cnGLFJ4cArdpLUQ0ihf_c2hIn-XNN7R7AUypiMZo-bA";
const whoisApiKey = "at_tvtKXq0ep0zxbNXmYz3m3FVkmdOlE";
const BASE_URL      = "https://api.affinity.co";
const WHOIS_BASE_URL = "https://reverse-whois.whoisxmlapi.com/api/v2";
const HEADERS       = {
  "Authorization": "Basic " + btoa(":" + affinityApiKey),
  "Content-Type":  "application/json"
};

// ───────────────────────────────────────────────────────────
// SEARCH VARIANT GENERATORS
// ───────────────────────────────────────────────────────────

/** Generate search variants for personal profiles */
function generatePersonalSearchVariants(fullName) {
  const variants = new Set();
  const nameParts = fullName.trim().split(/\s+/).filter(Boolean);

  if (nameParts.length === 0) return [fullName];

  if (nameParts.length === 2) {
    // First + Last name only
    const [first, last] = nameParts;
    variants.add(`${first} ${last}`);
  } else if (nameParts.length === 3) {
    const [first, middle, last] = nameParts;

    // Check if middle name is just a letter or letter with period
    const isMiddleInitial = middle.length === 1 || (middle.length === 2 && middle.endsWith('.'));

    if (isMiddleInitial) {
      // If middle is just initial, only search first + last
      variants.add(`${first} ${last}`);
    } else {
      // If middle is a full name, search all combinations
      variants.add(`${first} ${middle}`);
      variants.add(`${first} ${last}`);
      variants.add(`${middle} ${last}`);
    }
  } else if (nameParts.length > 3) {
    // For more than 3 names, just use first + last
    variants.add(`${nameParts[0]} ${nameParts[nameParts.length - 1]}`);
  } else {
    // Single name
    variants.add(fullName);
  }

  return Array.from(variants);
}

/** Generate search variants for company profiles */
function generateCompanySearchVariants(companyName) {
  const variants = new Set([companyName]);

  // Common company word replacements
  const replacements = [
    { from: 'incorporated', to: 'inc' },
    { from: 'inc', to: 'incorporated' },
    { from: 'corporation', to: 'corp' },
    { from: 'corp', to: 'corporation' },
    { from: 'limited', to: 'ltd' },
    { from: 'ltd', to: 'limited' },
    { from: 'company', to: 'co' },
    { from: 'co', to: 'company' },
    { from: 'technologies', to: 'tech' },
    { from: 'tech', to: 'technologies' },
    { from: 'and', to: '&' },
    { from: '&', to: 'and' }
  ];

  // Apply replacements
  const lowerName = companyName.toLowerCase();
  replacements.forEach(({ from, to }) => {
    const fromRegex = new RegExp(`\\b${from}\\b`, 'gi');
    if (lowerName.includes(from)) {
      variants.add(companyName.replace(fromRegex, to));
    }
  });

  // Remove common legal suffixes
  const legalSuffixes = [
    /\b(inc|incorporated|corp|corporation|llc|ltd|limited|lp|llp|plc)\.?\b$/i,
    /\b(holdings|group|solutions|technologies|systems|partners|ventures|capital)\.?\b$/i
  ];

  legalSuffixes.forEach(suffix => {
    const withoutSuffix = companyName.replace(suffix, '').trim();
    if (withoutSuffix !== companyName && withoutSuffix.length > 0) {
      variants.add(withoutSuffix);
    }
  });

  // Handle "The" prefix
  if (companyName.toLowerCase().startsWith('the ')) {
    variants.add(companyName.substring(4));
  } else {
    variants.add('The ' + companyName);
  }

  return Array.from(variants);
}

// ───────────────────────────────────────────────────────────

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  // ─── 1) GET API KEY ─────────────────────────────────────
  if (request.type === 'getApiKey') {
    sendResponse({ apiKey: affinityApiKey });
    return;
  }

  // ─── 1.5) FETCH OPPORTUNITY DETAILS ─────────────────────
  if (request.type === 'fetchOpportunityDetails') {
    (async () => {
      try {
        // Validate the opportunity ID
        if (!request.opportunityId) {
          throw new Error('No opportunity ID provided');
        }

        // Validate API key
        if (!affinityApiKey) {
          throw new Error('No API key available');
        }

        console.log(`[background.js] API Key available: ${affinityApiKey ? 'Yes' : 'No'}`);
        console.log(`[background.js] API Key length: ${affinityApiKey ? affinityApiKey.length : 0}`);

        console.log(`[background.js] Fetching opportunity: ${request.opportunityId}`);
        console.log(`[background.js] API URL: ${BASE_URL}/opportunities/${request.opportunityId}`);
        console.log(`[background.js] Headers:`, HEADERS);

        // Fetch the opportunity data
        console.log(`[background.js] Attempting to fetch opportunity with ID: ${request.opportunityId}`);

        // Fetch the opportunity data
        let opportunity;
        try {
          const opportunityResponse = await fetch(`${BASE_URL}/opportunities/${request.opportunityId}`, {
            headers: HEADERS
          });

          if (!opportunityResponse.ok) {
            const errorText = await opportunityResponse.text();
            console.error(`[background.js] Opportunity fetch failed: ${opportunityResponse.status}`, errorText);

            // Try a different approach - maybe it's a list entry ID instead of an opportunity ID
            console.log(`[background.js] Trying to fetch list entry with ID: ${request.opportunityId}`);
            const listEntryResponse = await fetch(`${BASE_URL}/list-entries/${request.opportunityId}`, {
              headers: HEADERS
            });

            if (listEntryResponse.ok) {
              const listEntry = await listEntryResponse.json();
              console.log('[background.js] List entry data:', listEntry);

              if (listEntry.opportunity_id) {
                console.log(`[background.js] Found opportunity ID ${listEntry.opportunity_id} from list entry`);

                // Now fetch the opportunity with the correct ID
                const newOpportunityResponse = await fetch(`${BASE_URL}/opportunities/${listEntry.opportunity_id}`, {
                  headers: HEADERS
                });

                if (newOpportunityResponse.ok) {
                  opportunity = await newOpportunityResponse.json();
                } else {
                  throw new Error(`Failed to fetch opportunity from list entry: ${newOpportunityResponse.status}`);
                }
              } else {
                throw new Error('List entry does not contain an opportunity ID');
              }
            } else {
              throw new Error(`Failed to fetch opportunity: ${opportunityResponse.status} - ${errorText}`);
            }
          } else {
            opportunity = await opportunityResponse.json();
          }

          console.log('[background.js] Opportunity data:', opportunity);
        } catch (error) {
          console.error('[background.js] Error in opportunity fetch:', error);
          throw error;
        }

        // Fetch notes for this opportunity
        console.log(`[background.js] Fetching notes for opportunity: ${opportunity.id || request.opportunityId}`);

        // Get the correct opportunity ID
        const opportunityId = opportunity?.id || request.opportunityId;

        // Try different API endpoints for notes
        let notesEndpoint = `${BASE_URL}/notes?opportunity_id=${opportunityId}`;
        console.log(`[background.js] Notes API URL (first attempt): ${notesEndpoint}`);

        let notesResponse = await fetch(notesEndpoint, {
          headers: HEADERS
        });

        // If the first endpoint fails, try the alternative
        if (!notesResponse.ok) {
          console.log(`[background.js] First notes endpoint failed, trying alternative`);
          notesEndpoint = `${BASE_URL}/opportunities/${opportunityId}/notes`;
          console.log(`[background.js] Notes API URL (second attempt): ${notesEndpoint}`);

          notesResponse = await fetch(notesEndpoint, {
            headers: HEADERS
          });
        }

        if (!notesResponse.ok) {
          const errorText = await notesResponse.text();
          console.error(`[background.js] Notes fetch failed: ${notesResponse.status}`, errorText);

          // Don't throw an error here, just log it and continue with empty notes
          console.warn(`[background.js] Continuing with empty notes list`);
          return sendResponse({
            success: true,
            details: {
              id: opportunityId,
              name: opportunity?.name || opportunity?.title || 'Unknown Opportunity',
              notes: [],
              status: null,
              statusField: null,
              statusOptions: [],
              listEntryId: opportunity?.list_entries?.[0]?.id
            }
          });
        }

        const notesData = await notesResponse.json();
        console.log('[background.js] Notes data:', notesData);

        // The notes response might be an array directly or an object with a notes property
        const notes = Array.isArray(notesData) ? notesData : notesData;

        // Fetch list entry to get field values (status)
        const listEntryId = opportunity.list_entries?.[0]?.id;
        console.log(`[background.js] List entry ID: ${listEntryId}`);

        let status = null;
        let statusField = null;
        let statusOptions = [];

        if (listEntryId) {
          // Get the list ID
          const listId = opportunity.list_entries[0].list_id;
          console.log(`[background.js] List ID: ${listId}`);

          // Fetch list metadata to get the status field
          console.log(`[background.js] Fetching list metadata for list: ${listId}`);
          console.log(`[background.js] List API URL: ${BASE_URL}/lists/${listId}`);

          try {
            const listResponse = await fetch(`${BASE_URL}/lists/${listId}`, {
              headers: HEADERS
            });

            if (listResponse.ok) {
              const listData = await listResponse.json();
              console.log('[background.js] List data:', listData);

              // Try to find the Status field - it might be named differently or have different capitalization
              statusField = listData.fields.find(f =>
                f.name === 'Status' ||
                f.name === 'status' ||
                f.name.toLowerCase() === 'status' ||
                f.name.toLowerCase().includes('status')
              );
              console.log('[background.js] Status field:', statusField);

              if (statusField) {
                statusOptions = statusField.dropdown_options || [];
                console.log('[background.js] Status options:', statusOptions);

                // Fetch the current status value
                console.log(`[background.js] Fetching field values for opportunity: ${request.opportunityId}, field: ${statusField.id}`);

                // Try both endpoints for field values
                try {
                  console.log(`[background.js] Trying first field values endpoint: /field-values?opportunity_id=${request.opportunityId}&field_id=${statusField.id}`);
                  const fieldValuesResponse = await fetch(
                    `${BASE_URL}/field-values?opportunity_id=${request.opportunityId}&field_id=${statusField.id}`, {
                    headers: HEADERS
                  });

                  if (fieldValuesResponse.ok) {
                    const fieldValues = await fieldValuesResponse.json();
                    console.log('[background.js] Field values:', fieldValues);

                    if (fieldValues.length > 0) {
                      const statusValue = fieldValues[0].value;
                      console.log('[background.js] Status value from API:', statusValue, 'type:', typeof statusValue);
                      console.log('[background.js] Raw status value:', JSON.stringify(statusValue));
                      console.log('[background.js] Status options:', statusOptions.map(opt => ({
                        id: opt.id,
                        text: opt.text,
                        idType: typeof opt.id,
                        idStringified: JSON.stringify(opt.id)
                      })));

                      // Try different comparison methods
                      const exactMatch = statusOptions.find(opt => opt.id === statusValue);
                      const stringMatch = statusOptions.find(opt => String(opt.id) === String(statusValue));
                      const numberMatch = statusOptions.find(opt => Number(opt.id) === Number(statusValue));

                      console.log('[background.js] Matching attempts:', {
                        exactMatch,
                        stringMatch,
                        numberMatch
                      });

                      // Use the first successful match
                      status = exactMatch || stringMatch || numberMatch || null;

                      // If still no match, try a more aggressive approach
                      if (!status) {
                        console.log('[background.js] No match found with standard methods, trying more aggressive matching');

                        // Try to match by removing all non-numeric characters
                        const numericStatusValue = String(statusValue).replace(/\D/g, '');
                        const numericMatch = statusOptions.find(opt =>
                          String(opt.id).replace(/\D/g, '') === numericStatusValue
                        );

                        if (numericMatch) {
                          console.log('[background.js] Found match using numeric-only comparison:', numericMatch);
                          status = numericMatch;
                        }
                      }

                      console.log('[background.js] Final status selection:', status);
                    } else {
                      console.log('[background.js] No field values found');
                    }
                  } else {
                    const errorText = await fieldValuesResponse.text();
                    console.log(`[background.js] Failed to fetch field values: ${fieldValuesResponse.status}`, errorText);

                    // Try alternative endpoint
                    console.log(`[background.js] Trying alternative field values endpoint: /opportunities/${request.opportunityId}/field-values?field_id=${statusField.id}`);
                    const altFieldValuesResponse = await fetch(
                      `${BASE_URL}/opportunities/${request.opportunityId}/field-values?field_id=${statusField.id}`, {
                      headers: HEADERS
                    });

                    if (altFieldValuesResponse.ok) {
                      const altFieldValues = await altFieldValuesResponse.json();
                      console.log('[background.js] Alternative field values:', altFieldValues);

                      if (Array.isArray(altFieldValues) && altFieldValues.length > 0) {
                        const statusValue = altFieldValues[0].value;
                        console.log('[background.js] Status value from alternative API:', statusValue, 'type:', typeof statusValue);

                        // Try to find the matching status option
                        status = statusOptions.find(opt =>
                          String(opt.id) === String(statusValue) ||
                          Number(opt.id) === Number(statusValue)
                        ) || null;

                        console.log('[background.js] Status from alternative endpoint:', status);
                      }
                    } else {
                      console.log(`[background.js] Alternative endpoint also failed: ${altFieldValuesResponse.status}`);
                    }
                  }
                } catch (fieldValueError) {
                  console.error('[background.js] Error fetching field values:', fieldValueError);
                }
              } else {
                console.log('[background.js] Status field not found');
              }
            } else {
              const errorText = await listResponse.text();
              console.log(`[background.js] Failed to fetch list metadata: ${listResponse.status}`, errorText);
            }
          } catch (listError) {
            console.error('[background.js] Error fetching list metadata:', listError);
          }
        } else {
          console.log('[background.js] No list entry ID found');
        }

        // Store any field values we found
        let fieldValues = [];
        let listEntryData = null;

        try {
          // Fetch all field values for this opportunity to include in the response
          console.log(`[background.js] Fetching field values for opportunity: ${opportunityId}`);
          const allFieldValuesResponse = await fetch(
            `${BASE_URL}/field-values?opportunity_id=${opportunityId}`, {
            headers: HEADERS
          });

          if (allFieldValuesResponse.ok) {
            fieldValues = await allFieldValuesResponse.json();
            console.log('[background.js] All field values for opportunity:', fieldValues);
          } else {
            console.log(`[background.js] Failed to fetch field values: ${allFieldValuesResponse.status}`);
          }

          // Also fetch the list entry data
          if (listEntryId) {
            console.log(`[background.js] Fetching list entry data: ${listEntryId}`);
            const listEntryResponse = await fetch(
              `${BASE_URL}/list-entries/${listEntryId}`, {
              headers: HEADERS
            });

            if (listEntryResponse.ok) {
              listEntryData = await listEntryResponse.json();
              console.log('[background.js] List entry data:', listEntryData);
            } else {
              console.log(`[background.js] Failed to fetch list entry: ${listEntryResponse.status}`);
            }
          }
        } catch (error) {
          console.error('[background.js] Error fetching additional data:', error);
        }

        sendResponse({
          success: true,
          details: {
            id: opportunityId,
            name: opportunity?.name || opportunity?.title || 'Unknown Opportunity',
            notes,
            status,
            statusField,
            statusOptions,
            listEntryId,
            fieldValues,
            listEntryData,
            opportunity // Include the full opportunity object
          }
        });
      } catch (error) {
        console.error('[background.js] fetchOpportunityDetails error', error);
        sendResponse({ success: false, error: error.message });
      }
    })();
    return true; // keep channel open
  }

  // ─── 1.6) UPDATE OPPORTUNITY ─────────────────────────────
  if (request.type === 'updateOpportunity') {
    (async () => {
      try {
        console.log('[background.js] Update opportunity request:', request);

        // Always update status if we have the required fields
        if (request.statusFieldId && request.listEntryId && request.newStatusId) {
          console.log('[background.js] Updating status:', {
            field_id: request.statusFieldId,
            entity_id: request.opportunityId,
            list_entry_id: request.listEntryId,
            value: request.newStatusId,
            oldValue: request.oldStatusId
          });

          try {
            // First check if there's an existing field value that needs to be updated
            const fieldValuesResponse = await fetch(
              `${BASE_URL}/field-values?opportunity_id=${request.opportunityId}&field_id=${request.statusFieldId}`, {
              headers: HEADERS
            });

            let existingFieldValueId = null;

            if (fieldValuesResponse.ok) {
              const fieldValues = await fieldValuesResponse.json();
              console.log('[background.js] Existing field values:', fieldValues);

              if (fieldValues.length > 0) {
                existingFieldValueId = fieldValues[0].id;
              }
            } else {
              const errorText = await fieldValuesResponse.text();
              console.error(`[background.js] Failed to fetch field values: ${fieldValuesResponse.status}`, errorText);
            }

            let statusResponse;

            if (existingFieldValueId) {
              // Update existing field value
              console.log(`[background.js] Updating existing field value: ${existingFieldValueId}`);
              statusResponse = await fetch(`${BASE_URL}/field-values/${existingFieldValueId}`, {
                method: 'PUT',
                headers: HEADERS,
                body: JSON.stringify({
                  value: request.newStatusId
                })
              });
            } else {
              // Create new field value
              console.log('[background.js] Creating new field value');
              statusResponse = await fetch(`${BASE_URL}/field-values`, {
                method: 'POST',
                headers: HEADERS,
                body: JSON.stringify({
                  field_id: parseInt(request.statusFieldId, 10),
                  entity_id: parseInt(request.opportunityId, 10),
                  list_entry_id: parseInt(request.listEntryId, 10),
                  value: parseInt(request.newStatusId, 10)
                })
              });
            }

            if (!statusResponse.ok) {
              const errorText = await statusResponse.text();
              console.error(`[background.js] Status update failed: ${statusResponse.status}`, errorText);
              throw new Error(`Failed to update status: ${statusResponse.status} - ${errorText}`);
            } else {
              const responseData = await statusResponse.json();
              console.log('[background.js] Status update response:', responseData);
            }
          } catch (statusError) {
            console.error('[background.js] Error updating status:', statusError);
            throw statusError;
          }
        } else {
          console.log('[background.js] No status change to update');
        }

        // Add new note if provided
        if (request.hasNewNote) {
          console.log('[background.js] Adding new note:', {
            content: request.newNote,
            opportunity_ids: [request.opportunityId]
          });

          // The API endpoint for creating a note for an opportunity
          const noteResponse = await fetch(`${BASE_URL}/notes`, {
            method: 'POST',
            headers: HEADERS,
            body: JSON.stringify({
              content: request.newNote,
              opportunity_ids: [parseInt(request.opportunityId, 10)],
              type: 2 // Regular note type
            })
          });

          if (!noteResponse.ok) {
            const errorText = await noteResponse.text();
            console.error(`[background.js] Note creation failed: ${noteResponse.status}`, errorText);
            throw new Error(`Failed to add note: ${noteResponse.status}`);
          } else {
            const responseData = await noteResponse.json();
            console.log('[background.js] Note creation response:', responseData);
          }
        } else {
          console.log('[background.js] No new note to add');
        }

        console.log('[background.js] Update completed successfully');
        sendResponse({ success: true });
      } catch (error) {
        console.error('[background.js] updateOpportunity error', error);
        sendResponse({ success: false, error: error.message });
      }
    })();
    return true; // keep channel open
  }

  // ─── 2) SEARCH EXISTING OPPORTUNITIES ─────────────────────
  if (request.type === 'searchAffinityByName') {
    (async () => {
      const raw = request.name.trim();
      const isCompanyPage = request.isCompanyPage || false;

      let searchVariants = [];

      if (isCompanyPage) {
        // Company search variations
        searchVariants = generateCompanySearchVariants(raw);
      } else {
        // Personal profile search variations
        searchVariants = generatePersonalSearchVariants(raw);
      }

      console.log('[background.js] search variants:', searchVariants);

      try {
        // fetch all /opportunities?term= in parallel
        const results = await Promise.all(
          searchVariants.map(term =>
            fetch(
              `${BASE_URL}/opportunities?term=${encodeURIComponent(term)}`,
              { headers: HEADERS }
            )
            .then(r => r.ok ? r.json() : null)
            .catch(() => null)
          )
        );

        // dedupe by opportunity.id
        const seen = new Map();
        results.forEach(json => {
          if (!json || !Array.isArray(json.opportunities)) return;
          json.opportunities.forEach(item => {
            if (!seen.has(item.id)) {
              seen.set(item.id, {
                id:   item.id,
                name: item.title || item.name || '(no title)'
              });
            }
          });
        });

        const matches = Array.from(seen.values());
        console.log(
          `[background.js] Total unique opportunities found: ${matches.length}`
        );

        sendResponse(
          matches.length
            ? { matches }
            : { matches: [], message: 'No opportunities found.' }
        );
      } catch (err) {
        console.error('[background.js] search error', err);
        sendResponse({ matches: [], message: 'Error contacting Affinity API.' });
      }
    })();
    return true; // keep channel open
  }

  // ─── 3) CREATE OPPORTUNITY + NOTES + STATUS + OWNERS + SOURCE ───
  if (request.type === 'createOpportunity') {
    (async () => {
      const { name, url: linkedinUrl, notes, source } = request;
      try {
        // 3a) Create Opportunity in list 85510
        const oppRes = await fetch(`${BASE_URL}/opportunities`, {
          method: 'POST',
          headers: HEADERS,
          body: JSON.stringify({ name, list_id: 85510 })
        });
        if (!oppRes.ok) throw new Error(`Opp creation failed: ${oppRes.status}`);
        const oppJson     = await oppRes.json();
        const oppId       = oppJson.id;
        const listEntryId = oppJson.list_entries?.[0]?.id;
        if (!listEntryId) throw new Error('listEntryId missing on oppJson');

        console.log('[background.js] Created Opportunity:', oppJson);

        // 3b) Add Note for LinkedIn URL
        const note1 = await fetch(`${BASE_URL}/notes`, {
          method: 'POST',
          headers: HEADERS,
          body: JSON.stringify({
            content:          linkedinUrl,
            opportunity_ids: [oppId],
            type:             2
          })
        });
        if (!note1.ok) throw new Error(`URL note failed: ${note1.status}`);

        // 3c) Add Note for user-entered notes (if any)
        if (notes) {
          const note2 = await fetch(`${BASE_URL}/notes`, {
            method: 'POST',
            headers: HEADERS,
            body: JSON.stringify({
              content:          notes,
              opportunity_ids: [oppId],
              type:             2
            })
          });
          if (!note2.ok) throw new Error(`User note failed: ${note2.status}`);
        }

        // 3d) Fetch list metadata to get Status, Owners & Source fields
        const listRes = await fetch(`${BASE_URL}/lists/85510`, { headers: HEADERS });
        if (!listRes.ok) throw new Error(`List metadata GET failed: ${listRes.status}`);
        const listJson = await listRes.json();
        console.log('[background.js] listJson.fields:', listJson.fields);

        const statusField = listJson.fields.find(f => f.name === 'Status');
        const ownersField = listJson.fields.find(f => f.name.toLowerCase().includes('owner'));
        const sourceField = listJson.fields.find(f => f.name === 'Source');
        if (!statusField) throw new Error('Status field not found');
        if (!ownersField) throw new Error('Owners field not found');
        if (!sourceField) throw new Error('Source field not found');

        console.log(
          '[background.js] Fields →',
          'Status:', statusField.id,
          'Owners:', ownersField.id,
          'Source:', sourceField.id
        );

        // 3e) Assign Status = "New"
        const newOpt = statusField.dropdown_options.find(o => o.text === 'New');
        if (newOpt) {
          const fv1 = await fetch(`${BASE_URL}/field-values`, {
            method: 'POST',
            headers: HEADERS,
            body: JSON.stringify({
              field_id:      statusField.id,
              entity_id:     oppId,
              list_entry_id: listEntryId,
              value:         newOpt.id
            })
          });
          if (!fv1.ok) throw new Error(`Status FV failed: ${fv1.status}`);
        } else {
          console.warn('[background.js] "New" option missing; skipping status set.');
        }

        // 3f) Assign Owners = Hanna Lewis (person lookup)
        const pRes = await fetch(
          `${BASE_URL}/persons?term=${encodeURIComponent('<EMAIL>')}`,
          { headers: HEADERS }
        );
        if (!pRes.ok) throw new Error(`Persons lookup failed: ${pRes.status}`);
        const pJson = await pRes.json();
        console.log('[background.js] Persons response:', pJson.persons);

        const hanna = (pJson.persons || []).find(p =>
          p.emails?.includes('<EMAIL>')
        ) || pJson.persons?.[0];
        if (hanna) {
          const fv2 = await fetch(`${BASE_URL}/field-values`, {
            method: 'POST',
            headers: HEADERS,
            body: JSON.stringify({
              field_id:      ownersField.id,
              entity_id:     oppId,
              list_entry_id: listEntryId,
              value:         hanna.id
            })
          });
          if (!fv2.ok) throw new Error(`Owners FV failed: ${fv2.status}`);
        } else {
          console.warn('[background.js] Hanna Lewis not found; skipping owner set.');
        }

        // 3g) Debug Source options
        console.log(
          '[background.js] Source dropdown_options:',
          sourceField.dropdown_options
        );

        // 3h) Assign Source by TEXT (required String type)
        if (source) {
          const fv3 = await fetch(`${BASE_URL}/field-values`, {
            method: 'POST',
            headers: HEADERS,
            body: JSON.stringify({
              field_id:      sourceField.id,
              entity_id:     oppId,
              list_entry_id: listEntryId,
              value:         source
            })
          });
          if (!fv3.ok) {
            const errBody = await fv3.text();
            console.error('[background.js] Source FV 422 body:', errBody);
            throw new Error(`Source FV failed: ${fv3.status}`);
          }
        }

        sendResponse({ success: true });
      } catch (err) {
        console.error('[background.js] createOpportunity error', err);
        sendResponse({ success: false, error: err.message });
      }
    })();
    return true; // keep channel open
  }

  // ─── 3.1) CREATE OPPORTUNITY + NOTES + STATUS "TO REACH OUT" + OWNERS + SOURCE ───
  if (request.type === 'createOpportunityReachOut') {
    (async () => {
      const { name, url: linkedinUrl, notes, source } = request;
      try {
        // 3a) Create Opportunity in list 85510
        const oppRes = await fetch(`${BASE_URL}/opportunities`, {
          method: 'POST',
          headers: HEADERS,
          body: JSON.stringify({ name, list_id: 85510 })
        });
        if (!oppRes.ok) throw new Error(`Opp creation failed: ${oppRes.status}`);
        const oppJson     = await oppRes.json();
        const oppId       = oppJson.id;
        const listEntryId = oppJson.list_entries?.[0]?.id;
        if (!listEntryId) throw new Error('listEntryId missing on oppJson');

        console.log('[background.js] Created Opportunity for Reach Out:', oppJson);

        // 3b) Add Note for LinkedIn URL
        const note1 = await fetch(`${BASE_URL}/notes`, {
          method: 'POST',
          headers: HEADERS,
          body: JSON.stringify({
            content:          linkedinUrl,
            opportunity_ids: [oppId],
            type:             2
          })
        });
        if (!note1.ok) throw new Error(`URL note failed: ${note1.status}`);

        // 3c) Add Note for user-entered notes (if any)
        if (notes) {
          const note2 = await fetch(`${BASE_URL}/notes`, {
            method: 'POST',
            headers: HEADERS,
            body: JSON.stringify({
              content:          notes,
              opportunity_ids: [oppId],
              type:             2
            })
          });
          if (!note2.ok) throw new Error(`User note failed: ${note2.status}`);
        }

        // 3d) Fetch list metadata to get Status, Owners & Source fields
        const listRes = await fetch(`${BASE_URL}/lists/85510`, { headers: HEADERS });
        if (!listRes.ok) throw new Error(`List metadata GET failed: ${listRes.status}`);
        const listJson = await listRes.json();
        console.log('[background.js] listJson.fields:', listJson.fields);

        const statusField = listJson.fields.find(f => f.name === 'Status');
        const ownersField = listJson.fields.find(f => f.name.toLowerCase().includes('owner'));
        const sourceField = listJson.fields.find(f => f.name === 'Source');
        if (!statusField) throw new Error('Status field not found');
        if (!ownersField) throw new Error('Owners field not found');
        if (!sourceField) throw new Error('Source field not found');

        console.log(
          '[background.js] Fields →',
          'Status:', statusField.id,
          'Owners:', ownersField.id,
          'Source:', sourceField.id
        );

        // 3e) Assign Status = "To Reach Out"
        const reachOutOpt = statusField.dropdown_options.find(o => o.text === 'To Reach Out');
        if (reachOutOpt) {
          const fv1 = await fetch(`${BASE_URL}/field-values`, {
            method: 'POST',
            headers: HEADERS,
            body: JSON.stringify({
              field_id:      statusField.id,
              entity_id:     oppId,
              list_entry_id: listEntryId,
              value:         reachOutOpt.id
            })
          });
          if (!fv1.ok) throw new Error(`Status FV failed: ${fv1.status}`);
        } else {
          console.warn('[background.js] "To Reach Out" option missing; skipping status set.');
        }

        // 3f) Assign Owners = Hanna Lewis (person lookup)
        const pRes = await fetch(
          `${BASE_URL}/persons?term=${encodeURIComponent('<EMAIL>')}`,
          { headers: HEADERS }
        );
        if (!pRes.ok) throw new Error(`Persons lookup failed: ${pRes.status}`);
        const pJson = await pRes.json();
        console.log('[background.js] Persons response:', pJson.persons);

        const hanna = (pJson.persons || []).find(p =>
          p.emails?.includes('<EMAIL>')
        ) || pJson.persons?.[0];
        if (hanna) {
          const fv2 = await fetch(`${BASE_URL}/field-values`, {
            method: 'POST',
            headers: HEADERS,
            body: JSON.stringify({
              field_id:      ownersField.id,
              entity_id:     oppId,
              list_entry_id: listEntryId,
              value:         hanna.id
            })
          });
          if (!fv2.ok) throw new Error(`Owners FV failed: ${fv2.status}`);
        } else {
          console.warn('[background.js] Hanna Lewis not found; skipping owner set.');
        }

        // 3g) Debug Source options
        console.log(
          '[background.js] Source dropdown_options:',
          sourceField.dropdown_options
        );

        // 3h) Assign Source by TEXT (required String type)
        if (source) {
          const fv3 = await fetch(`${BASE_URL}/field-values`, {
            method: 'POST',
            headers: HEADERS,
            body: JSON.stringify({
              field_id:      sourceField.id,
              entity_id:     oppId,
              list_entry_id: listEntryId,
              value:         source
            })
          });
          if (!fv3.ok) {
            const errBody = await fv3.text();
            console.error('[background.js] Source FV 422 body:', errBody);
            throw new Error(`Source FV failed: ${fv3.status}`);
          }
        }

        sendResponse({ success: true });
      } catch (err) {
        console.error('[background.js] createOpportunityReachOut error', err);
        sendResponse({ success: false, error: err.message });
      }
    })();
    return true; // keep channel open
  }

  // ─── 4) REVERSE WHOIS LOOKUP ─────────────────────────────────
  if (request.type === 'reverseWhoisLookup') {
    (async () => {
      try {
        const { name } = request;
        console.log('[background.js] Starting reverse WHOIS lookup for:', name);

        // Parse name and generate search combinations
        const nameVariations = generateNameVariations(name);
        console.log('[background.js] Name variations:', nameVariations);

        // Search for domains using each name variation
        const allDomains = new Set();
        const searchResults = [];

        for (const searchName of nameVariations) {
          try {
            console.log('[background.js] Searching domains for:', searchName);
            const domains = await searchDomainsByName(searchName);

            if (domains && domains.length > 0) {
              searchResults.push({
                searchTerm: searchName,
                domains: domains
              });

              // Add to overall set for deduplication
              domains.forEach(domain => allDomains.add(domain));
            }
          } catch (error) {
            console.error(`[background.js] Error searching for ${searchName}:`, error);
          }
        }

        const uniqueDomains = Array.from(allDomains);
        console.log(`[background.js] Found ${uniqueDomains.length} unique domains`);

        sendResponse({
          success: true,
          domains: uniqueDomains,
          searchResults: searchResults,
          totalFound: uniqueDomains.length
        });

      } catch (error) {
        console.error('[background.js] Reverse WHOIS lookup error:', error);
        sendResponse({
          success: false,
          error: error.message,
          domains: []
        });
      }
    })();
    return true; // keep channel open
  }

  // ─── 5) ADD TO TALENT BANK ─────────────────────────────────
  if (request.type === 'addToTalentBank') {
    (async () => {
      try {
        const { data } = request;
        console.log('[background.js] Adding to talent bank:', data);

        // Airtable API credentials
        const AIRTABLE_API_KEY = "**********************************************************************************";
        const AIRTABLE_BASE_ID = "appV2fDNZEZF9mmZn";
        const AIRTABLE_TABLE_ID = "tblaSt6RWB116z8xV";

        // Prepare the record for Airtable
        const record = {
          fields: {
            "Name": data.name,
            "Background Overview": data.about || "",
            "LinkedIn": data.profileUrl,
            "Functions": data.functions,
            "Purpose": data.purpose,
            "BWC Contact": data.bwcContact,
            "When to reach out again": data.reachOutDate || null,
            "Specific portcos that might be interested": data.portcosInterest || null
          }
        };

        console.log('[background.js] Sending record to Airtable:', record);

        const apiUrl = `https://api.airtable.com/v0/${AIRTABLE_BASE_ID}/${AIRTABLE_TABLE_ID}`;
        console.log('[background.js] Airtable API URL:', apiUrl);

        // Send to Airtable
        const response = await fetch(apiUrl, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${AIRTABLE_API_KEY}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            records: [record]
          })
        });

        console.log('[background.js] Airtable response status:', response.status);

        if (!response.ok) {
          let errorData;
          try {
            errorData = await response.json();
          } catch (e) {
            errorData = { error: { message: await response.text() } };
          }
          console.error('[background.js] Airtable API error:', errorData);
          console.error('[background.js] Response headers:', [...response.headers.entries()]);
          throw new Error(`Airtable API error: ${response.status} - ${errorData.error?.message || errorData.error || 'Unknown error'}`);
        }

        const result = await response.json();
        console.log('[background.js] Airtable response:', result);

        sendResponse({ success: true, data: result });
      } catch (error) {
        console.error('[background.js] addToTalentBank error', error);
        sendResponse({ success: false, error: error.message });
      }
    })();
    return true; // keep channel open
  }
});

// ───────────────────────────────────────────────────────────
// REVERSE WHOIS HELPER FUNCTIONS
// ───────────────────────────────────────────────────────────

/**
 * Generate name variations for reverse WHOIS search
 * @param {string} fullName - The full name from LinkedIn profile
 * @returns {Array<string>} - Array of name variations to search
 */
function generateNameVariations(fullName) {
  if (!fullName || typeof fullName !== 'string') {
    return [];
  }

  const variations = [];

  // Clean and split the name
  const cleanName = fullName.trim();
  const words = cleanName.split(/\s+/).filter(word => word.length > 0);

  if (words.length === 0) {
    return [];
  }

  // Remove common titles
  const titles = ['dr', 'dr.', 'prof', 'prof.', 'mr', 'mr.', 'mrs', 'mrs.', 'ms', 'ms.', 'miss'];
  let nameWords = [...words];

  // Check if first word is a title and remove it
  if (nameWords.length > 1 && titles.includes(nameWords[0].toLowerCase())) {
    nameWords = nameWords.slice(1);
  }

  console.log('[background.js] Name words after title removal:', nameWords);

  if (nameWords.length === 0) {
    return [];
  }

  // Handle different name lengths
  if (nameWords.length === 1) {
    // Only one name - just search as is
    variations.push(nameWords[0]);
  } else if (nameWords.length === 2) {
    // First and Last name only
    const [first, last] = nameWords;
    variations.push(`${first} ${last}`);
  } else if (nameWords.length >= 3) {
    // Has middle name(s) - generate combinations
    const first = nameWords[0];
    const last = nameWords[nameWords.length - 1];
    const middleWords = nameWords.slice(1, -1);
    const middle = middleWords.join(' ');

    // Always include the full name combination
    variations.push(`${first} ${middle} ${last}`); // First + Middle + Last
    variations.push(`${first} ${last}`);           // First + Last

    // Only add "first + middle" and "middle + last" if middle name is not just an initial
    // Check if any middle word is longer than 1 character (not just an initial)
    const hasFullMiddleName = middleWords.some(word => word.length > 1);

    if (hasFullMiddleName) {
      variations.push(`${first} ${middle}`);         // First + Middle
      variations.push(`${middle} ${last}`);          // Middle + Last
    }
  }

  // Remove duplicates and empty strings
  const uniqueVariations = [...new Set(variations)].filter(v => v && v.trim().length > 0);

  console.log('[background.js] Generated name variations:', uniqueVariations);
  return uniqueVariations;
}

/**
 * Search for domains by name using WhoisXML API
 * @param {string} searchName - The name to search for
 * @returns {Array<string>} - Array of domain names
 */
async function searchDomainsByName(searchName) {
  if (!whoisApiKey || whoisApiKey === "YOUR_WHOISXML_API_KEY") {
    console.warn('[background.js] WhoisXML API key not configured');
    return [];
  }

  try {
    const requestBody = {
      apiKey: whoisApiKey,
      searchType: "current",
      mode: "purchase",
      punycode: true,
      basicSearchTerms: {
        include: [searchName]
      }
    };

    console.log('[background.js] Making WhoisXML API request for:', searchName);

    const response = await fetch(WHOIS_BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`WhoisXML API request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('[background.js] WhoisXML API response for', searchName, ':', data);

    // Extract domain names from the response
    if (data && Array.isArray(data.domainsList)) {
      return data.domainsList;
    } else if (data && data.domainsCount > 0) {
      // Handle case where domains are in a different format
      return data.domains || [];
    }

    return [];

  } catch (error) {
    console.error(`[background.js] Error searching domains for ${searchName}:`, error);
    return [];
  }
}
