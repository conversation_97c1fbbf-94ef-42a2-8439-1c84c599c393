// content.js

// Global config object
let extensionConfig = null;

/** Load configuration from config.json */
async function loadConfig() {
  try {
    const response = await fetch(chrome.runtime.getURL("config.json"));
    extensionConfig = await response.json();
    console.log('[content.js] Config loaded:', extensionConfig);
    return extensionConfig;
  } catch (error) {
    console.error('[content.js] Failed to load config:', error);
    // Fallback config
    extensionConfig = {
      user: "fallback",
      features: {
        addToAffinity: true,
        addToReachOut: false,
        findDomains: true,
        outreachTemplate: false
      },
      settings: {
        affinityButtonLabel: "Add to Affinity",
        outreachMessage: "template_version_1"
      }
    };
    return extensionConfig;
  }
}

/** 0) Remove any existing admin panels */
function removeAdminPanel() {
  document.querySelectorAll('.bluewire-admin-panel').forEach(el => el.remove());
}

/** 1) Grab the profile/company name from <h1> */
function getProfileName() {
  const h1 = document.querySelector('h1');
  return h1 ? h1.textContent.trim() : '';
}

/** 2) Build & insert our panel above the first <section> in <main> */
function insertAdminPanel({ matches = [], message = 'No matching records found in Affinity.', domains = [] }) {
  // cleanup
  removeAdminPanel();

  const main = document.querySelector('main');
  const firstSec = main?.querySelector('section');
  if (!firstSec) {
    console.warn('[content.js] no main section');
    return;
  }

  const isCompanyPage = /^\/company\/.+/.test(window.location.pathname);

  const panel = document.createElement('div');
  panel.classList.add('bluewire-admin-panel');
  panel.style.cssText = `
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #fff;
    margin-bottom: 16px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
  `;

  let html = `
    <h2 class="t-24 t-bold" style="margin:0 0 12px;color:#191919;">
      Blue Wire Capital Admin Panel
    </h2>
  `;

  if (matches.length > 0) {
    html += matches.length === 1
      ? '<p style="color:#666;">A potential match was found in Affinity:</p>'
      : '<p style="color:#666;">Several potential matches were found in Affinity:</p>';
    html += '<ul style="margin-top:8px;">' + matches.map(m =>
      `<li style="margin-left:16px;">
         <a href="#"
            class="affinity-opportunity-link"
            data-opportunity-id="${m.id}"
            style="color:#0a66c2;text-decoration:none;font-weight:500;">
           ${m.name}
         </a>
       </li>`
    ).join('') + '</ul>';
  } else {
    html += `<p style="color:#666;">${message}</p>`;
  }

  if (isCompanyPage) {
    const slug = getProfileName()
      .toLowerCase().trim()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9\-]/g, '');
    if (slug) {
      html += `
        <p style="margin-top:12px;">
          <a href="https://www.crunchbase.com/organization/${slug}"
             target="_blank"
             style="color:#0a66c2;text-decoration:none;font-weight:500;">
            View on Crunchbase
          </a>
        </p>
      `;
    }
  }



  // spacer - reduced height
  html += `<div style="height:8px;"></div>`;



  // Source dropdown with non-selectable placeholder
  html += `
    <div>
      <label for="affinity-source" style="font-weight:600;display:block;color:#191919;margin-bottom:4px;">
        Source:
      </label>
      <select
        id="affinity-source"
        style="
          width:100%;
          padding:8px 12px;
          border:1px solid #e0e0e0;
          border-radius:4px;
          box-sizing:border-box;
          margin-top:4px;
          height:40px;
          font-size:14px;
          color:#191919;
          background-color:#fff;
          box-shadow: 0 1px 3px rgba(0,0,0,0.08);
        "
      >
        <option value="" disabled selected hidden>
          Please select…
        </option>
        <option value="Signa">Signa</option>
        <option value="Referral">Referral</option>
        <option value="Landscape">Landscape</option>
        <option value="Demo Day/Conference">Demo Day/Conference</option>
        <option value="Cold Inbound">Cold Inbound</option>
        <option value="Other">Other</option>
      </select>
    </div>

    <div style="height:16px;"></div>
  `;

  // Notes + button
  html += `
    <div>
      <label for="affinity-notes" style="font-weight:600;display:block;color:#191919;margin-bottom:4px;">
        Notes for Affinity (optional):
      </label>
      <textarea
        id="affinity-notes"
        placeholder="Enter any additional notes…"
        style="
          width:100%;
          min-height:80px;
          padding:12px;
          border:1px solid #e0e0e0;
          border-radius:4px;
          box-sizing:border-box;
          margin-top:4px;
          font-size:14px;
          color:#191919;
          box-shadow: 0 1px 3px rgba(0,0,0,0.08);
        "
      ></textarea>
    </div>
    <div style="display: flex; gap: 10px; margin-top: 16px; justify-content: space-between; align-items: center;">
      <div style="display: flex; gap: 10px;">`;

  // Add to Reach Out button (conditional)
  if (extensionConfig?.features?.addToReachOut) {
    html += `
        <button
          id="add-to-reach-out"
          style="
            padding:6px 16px;
            background-color:#0a66c2;
            color:#fff;
            border:none;
            border-radius:24px;
            cursor:pointer;
            font-size:15px;
            font-weight:600;
            transition: background-color 0.2s;
            width: auto;
            min-width: 120px;
          "
          onmouseover="this.style.backgroundColor='#004182'"
          onmouseout="this.style.backgroundColor='#0a66c2'"
        >
          Add to Reach Out
        </button>`;
  }

  // Add to Affinity button (conditional)
  if (extensionConfig?.features?.addToAffinity) {
    const buttonLabel = extensionConfig?.settings?.affinityButtonLabel || "Add to Affinity";
    html += `
        <button
          id="add-to-affinity"
          style="
            padding:6px 16px;
            background-color:#0a66c2;
            color:#fff;
            border:none;
            border-radius:24px;
            cursor:pointer;
            font-size:15px;
            font-weight:600;
            transition: background-color 0.2s;
            width: auto;
            min-width: 120px;
          "
          onmouseover="this.style.backgroundColor='#004182'"
          onmouseout="this.style.backgroundColor='#0a66c2'"
        >
          ${buttonLabel}
        </button>`;
  }

  // Outreach Template button (conditional, only for individual profiles)
  if (extensionConfig?.features?.outreachTemplate && !isCompanyPage) {
    html += `
        <button
          id="outreach-template"
          style="
            padding:6px 16px;
            background-color:#0a66c2;
            color:#fff;
            border:none;
            border-radius:24px;
            cursor:pointer;
            font-size:15px;
            font-weight:600;
            transition: background-color 0.2s;
            width: auto;
            min-width: 120px;
          "
          onmouseover="this.style.backgroundColor='#004182'"
          onmouseout="this.style.backgroundColor='#0a66c2'"
        >
          Outreach Template
        </button>`;
  }

  // Add to Talent Bank button (conditional, only for individual profiles)
  if (extensionConfig?.features?.addToTalentBank && !isCompanyPage) {
    html += `
        <button
          id="add-to-talent-bank"
          style="
            padding:6px 16px;
            background-color:#0a66c2;
            color:#fff;
            border:none;
            border-radius:24px;
            cursor:pointer;
            font-size:15px;
            font-weight:600;
            transition: background-color 0.2s;
            width: auto;
            min-width: 120px;
          "
          onmouseover="this.style.backgroundColor='#004182'"
          onmouseout="this.style.backgroundColor='#0a66c2'"
        >
          Add to Talent Bank
        </button>`;
  }

  html += `
      </div>`;

  // Add Find Domains button (conditional, only for individual profiles)
  if (extensionConfig?.features?.findDomains && !isCompanyPage) {
    html += `
      <button
        id="find-domains"
        style="
          padding:6px 16px;
          background-color:#fff;
          color:#0a66c2;
          border:1px solid #0a66c2;
          border-radius:24px;
          cursor:pointer;
          font-size:15px;
          font-weight:600;
          transition: all 0.2s;
          width: auto;
          min-width: 100px;
        "
        onmouseover="this.style.backgroundColor='#f8f9fa'"
        onmouseout="this.style.backgroundColor='#fff'"
      >
        Find Domains
      </button>`;
  }

  html += `
    </div>
  `;

  panel.innerHTML = html;
  firstSec.parentNode.insertBefore(panel, firstSec);

  const btn = panel.querySelector('#add-to-affinity');
  const reachOutBtn = panel.querySelector('#add-to-reach-out');
  const notesInput = panel.querySelector('#affinity-notes');
  const sourceSelect = panel.querySelector('#affinity-source');
  const findDomainsBtn = panel.querySelector('#find-domains');
  const outreachTemplateBtn = panel.querySelector('#outreach-template');
  const talentBankBtn = panel.querySelector('#add-to-talent-bank');

  // Add event listeners for opportunity links
  panel.querySelectorAll('.affinity-opportunity-link').forEach(link => {
    link.addEventListener('click', (e) => {
      e.preventDefault();
      const opportunityId = link.getAttribute('data-opportunity-id');
      if (opportunityId) {
        showOpportunityPopup(opportunityId);
      }
    });
  });

  // Add event listener for "Add to Reach Out" button
  if (reachOutBtn) {
    reachOutBtn.addEventListener('click', async () => {
      // prevent using placeholder
      if (!sourceSelect.value) {
        alert('Please select a Source before adding to Affinity.');
        return;
      }

      reachOutBtn.disabled = true;
      reachOutBtn.textContent = 'Adding…';

      const name = getProfileName();
      const url  = location.href;
      const notes = notesInput.value.trim();
      const source = sourceSelect.value;

      const resp = await new Promise(resolve =>
        chrome.runtime.sendMessage(
          { type: 'createOpportunityReachOut', name, url, notes, source },
          resolve
        )
      );

      if (resp.success) {
        reachOutBtn.textContent = 'Added ✅';
      } else {
        reachOutBtn.textContent = 'Error ❌';
        console.error('[content.js] createOpportunityReachOut error', resp.error);
      }
    });
  }

  // Add event listener for Find Domains button
  if (findDomainsBtn) {
    findDomainsBtn.addEventListener('click', async () => {
      console.log('[content.js] Find Domains button clicked!');
      findDomainsBtn.disabled = true;
      findDomainsBtn.textContent = 'Searching...';

      try {
        const name = getProfileName();
        if (name) {
          await showDomainsPopup(name);
        } else {
          throw new Error('Could not get profile name');
        }
        findDomainsBtn.textContent = 'Find Domains';
        findDomainsBtn.disabled = false;
      } catch (error) {
        console.error('[content.js] Error finding domains:', error);
        findDomainsBtn.textContent = 'Error ❌';
        setTimeout(() => {
          findDomainsBtn.textContent = 'Find Domains';
          findDomainsBtn.disabled = false;
        }, 2000);
      }
    });
  } else {
    console.log('[content.js] Find Domains button not found in DOM');
  }

  // Add event listener for Outreach Template button
  if (outreachTemplateBtn) {
    outreachTemplateBtn.addEventListener('click', async () => {
      outreachTemplateBtn.disabled = true;
      outreachTemplateBtn.textContent = 'Copying...';

      try {
        console.log('[content.js] About to generate outreach template...');
        await copyOutreachTemplate();
        outreachTemplateBtn.textContent = 'Copied ✅';
        setTimeout(() => {
          outreachTemplateBtn.textContent = 'Outreach Template';
          outreachTemplateBtn.disabled = false;
        }, 2000);
      } catch (error) {
        console.error('[content.js] Error copying outreach template:', error);
        console.error('[content.js] Error stack:', error.stack);
        outreachTemplateBtn.textContent = 'Error ❌';
        setTimeout(() => {
          outreachTemplateBtn.textContent = 'Outreach Template';
          outreachTemplateBtn.disabled = false;
        }, 2000);
      }
    });
  } else {
    console.log('[content.js] Outreach Template button not found in DOM');
  }

  // Add event listener for Talent Bank button
  if (talentBankBtn) {
    talentBankBtn.addEventListener('click', async () => {
      talentBankBtn.disabled = true;
      talentBankBtn.textContent = 'Opening...';

      try {
        console.log('[content.js] About to show talent bank popup...');
        await showTalentBankPopup();
        talentBankBtn.textContent = 'Add to Talent Bank';
        talentBankBtn.disabled = false;
      } catch (error) {
        console.error('[content.js] Error showing talent bank popup:', error);
        talentBankBtn.textContent = 'Error ❌';
        setTimeout(() => {
          talentBankBtn.textContent = 'Add to Talent Bank';
          talentBankBtn.disabled = false;
        }, 2000);
      }
    });
  } else {
    console.log('[content.js] Talent Bank button not found in DOM');
  }

  // Add event listener for "Add to Affinity" button
  if (btn) {
    btn.addEventListener('click', async () => {
      // prevent using placeholder
      if (!sourceSelect.value) {
        alert('Please select a Source before adding to Affinity.');
        return;
      }

      btn.disabled = true;
      btn.textContent = 'Adding…';

      const name = getProfileName();
      const url  = location.href;
      const notes = notesInput.value.trim();
      const source = sourceSelect.value;

      const resp = await new Promise(resolve =>
        chrome.runtime.sendMessage(
          { type: 'createOpportunity', name, url, notes, source },
          resolve
        )
      );

      if (resp.success) {
        btn.textContent = 'Added ✅';
      } else {
        btn.textContent = 'Error ❌';
        console.error('[content.js] createOpportunity error', resp.error);
      }
    });
  }


}

/** 3) Ask background.js for matches */
function searchAffinityByName(name) {
  return new Promise(resolve =>
    chrome.runtime.sendMessage({ type: 'searchAffinityByName', name }, resolve)
  );
}

/** 3.1) Show domains popup and search for registered domains */
async function showDomainsPopup(name) {
  console.log('[content.js] Showing domains popup for:', name);

  // Create popup overlay
  const overlay = document.createElement('div');
  overlay.className = 'bwc-domains-popup-overlay';
  overlay.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
  `;

  // Create popup container
  const popup = document.createElement('div');
  popup.style.cssText = `
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    width: 500px;
    max-width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
  `;

  // Create header
  const header = document.createElement('div');
  header.style.cssText = `
    padding: 16px 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  `;

  const title = document.createElement('h2');
  title.textContent = 'Registered Domains';
  title.style.cssText = `
    font-size: 18px;
    font-weight: 600;
    margin: 0;
  `;

  const closeBtn = document.createElement('button');
  closeBtn.innerHTML = '&times;';
  closeBtn.style.cssText = `
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #666;
  `;
  closeBtn.addEventListener('click', () => overlay.remove());

  header.appendChild(title);
  header.appendChild(closeBtn);

  // Create content area
  const content = document.createElement('div');
  content.style.cssText = `
    padding: 20px;
  `;

  // Add loading message
  const loadingText = document.createElement('p');
  loadingText.textContent = 'Searching for registered domains...';
  loadingText.style.cssText = `
    margin: 0;
    color: #666;
    text-align: center;
  `;

  content.appendChild(loadingText);
  popup.appendChild(header);
  popup.appendChild(content);
  overlay.appendChild(popup);
  document.body.appendChild(overlay);

  // Close popup when clicking outside
  overlay.addEventListener('click', (e) => {
    if (e.target === overlay) {
      overlay.remove();
    }
  });

  // Close popup with Escape key
  const handleEscapeKey = (e) => {
    if (e.key === 'Escape') {
      overlay.remove();
      document.removeEventListener('keydown', handleEscapeKey);
    }
  };
  document.addEventListener('keydown', handleEscapeKey);

  // Perform the domain search
  try {
    const response = await new Promise(resolve =>
      chrome.runtime.sendMessage({ type: 'reverseWhoisLookup', name }, resolve)
    );

    console.log('[content.js] Reverse WHOIS response:', response);

    if (response && response.success) {
      displayDomainsInPopup(content, response.domains, response.totalFound);
    } else {
      displayDomainsErrorInPopup(content, response?.error || 'Failed to search for registered domains');
    }
  } catch (error) {
    console.error('[content.js] Error during reverse WHOIS lookup:', error);
    displayDomainsErrorInPopup(content, 'Error searching for registered domains');
  }
}

/** 3.1.1) Display domains in popup */
function displayDomainsInPopup(content, domains, totalFound) {
  // Clear loading message
  content.innerHTML = '';

  if (!domains || domains.length === 0) {
    content.innerHTML = '<p style="margin:0;color:#666;text-align:center;">No registered domains found.</p>';
    return;
  }

  // Create results summary
  const summary = document.createElement('p');
  if (totalFound === 1) {
    summary.textContent = '1 registered domain found:';
  } else {
    summary.textContent = `${totalFound} registered domains found:`;
  }
  summary.style.cssText = `
    margin: 0 0 16px 0;
    color: #666;
    font-weight: 500;
  `;
  content.appendChild(summary);

  // Create domains list
  const domainsList = document.createElement('ul');
  domainsList.style.cssText = `
    margin: 0;
    padding: 0;
    list-style: none;
    max-height: 400px;
    overflow-y: auto;
  `;

  domains.forEach(domain => {
    const listItem = document.createElement('li');
    listItem.style.cssText = `
      margin-bottom: 8px;
      padding: 8px 12px;
      background-color: #f8f9fa;
      border-radius: 4px;
      border: 1px solid #e9ecef;
    `;

    const link = document.createElement('a');
    link.href = `http://${domain}`;
    link.target = '_blank';
    link.textContent = domain;
    link.style.cssText = `
      color: #0a66c2;
      text-decoration: none;
      font-weight: 500;
    `;

    listItem.appendChild(link);
    domainsList.appendChild(listItem);
  });

  content.appendChild(domainsList);
}

/** 3.1.2) Display error in popup */
function displayDomainsErrorInPopup(content, errorMessage) {
  content.innerHTML = `
    <p style="margin:0;color:#666;text-align:center;">
      ${errorMessage}
    </p>
  `;
}



/** 3.2) Show opportunity popup */
function showOpportunityPopup(opportunityId) {
  // Instead of using an iframe, we'll directly communicate with the background script
  // to fetch and display the opportunity details

  // Create a popup overlay
  const overlay = document.createElement('div');
  overlay.className = 'bwc-popup-overlay';
  overlay.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
  `;

  // Create a loading indicator
  const loadingPopup = document.createElement('div');
  loadingPopup.style.cssText = `
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    width: 500px;
    max-width: 90%;
    padding: 20px;
    text-align: center;
  `;

  const loadingText = document.createElement('p');
  loadingText.textContent = 'Loading opportunity details...';
  loadingText.style.cssText = `
    font-size: 16px;
    color: #333;
  `;

  loadingPopup.appendChild(loadingText);
  overlay.appendChild(loadingPopup);
  document.body.appendChild(overlay);

  // Close popup when clicking outside
  overlay.addEventListener('click', (e) => {
    if (e.target === overlay) {
      overlay.remove();
    }
  });

  // Close popup with Escape key
  const handleEscapeKey = (e) => {
    if (e.key === 'Escape') {
      overlay.remove();
      document.removeEventListener('keydown', handleEscapeKey);
    }
  };
  document.addEventListener('keydown', handleEscapeKey);

  // First check if we have an API key
  chrome.runtime.sendMessage({ type: 'getApiKey' }, (response) => {
    if (response && response.apiKey) {
      console.log('[content.js] API key available, length:', response.apiKey.length);

      // Now proceed with fetching opportunity details
      fetchOpportunityDetails(opportunityId, overlay, loadingText, loadingPopup);
    } else {
      console.error('[content.js] No API key available');
      loadingText.textContent = 'Error: No API key available. Please check your extension settings.';

      // Add a retry button
      const retryBtn = document.createElement('button');
      retryBtn.textContent = 'Retry';
      retryBtn.style.cssText = `
        margin-top: 16px;
        padding: 8px 16px;
        background-color: #0a66c2;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
      `;
      retryBtn.addEventListener('click', () => {
        overlay.remove();
        showOpportunityPopup(opportunityId);
      });

      loadingPopup.appendChild(retryBtn);
    }
  });
}

/** 3.3) Fetch opportunity details */
function fetchOpportunityDetails(opportunityId, overlay, loadingText, loadingPopup) {
  // Request opportunity details from the background script
  console.log(`[content.js] Requesting opportunity details for ID: ${opportunityId}`);

  try {
    // Add a direct link to Affinity as a fallback
    const viewInAffinityBtn = document.createElement('a');
    viewInAffinityBtn.textContent = 'View in Affinity';
    viewInAffinityBtn.href = `https://bluewirecapital.affinity.co/opportunities/${opportunityId}`;
    viewInAffinityBtn.target = '_blank';
    viewInAffinityBtn.style.cssText = `
      display: block;
      margin: 16px auto 0;
      padding: 8px 16px;
      background-color: white;
      color: #0a66c2;
      border: 1px solid #0a66c2;
      border-radius: 4px;
      text-decoration: none;
      cursor: pointer;
      width: fit-content;
    `;
    loadingPopup.appendChild(viewInAffinityBtn);

    chrome.runtime.sendMessage(
      {
        type: 'fetchOpportunityDetails',
        opportunityId
      },
      (response) => {
        console.log(`[content.js] Received response for opportunity ${opportunityId}:`, response);

        if (response && response.success) {
          // Replace loading indicator with opportunity details
          displayOpportunityDetails(overlay, response.details, opportunityId);
        } else {
          // Show error message with details
          console.error('[content.js] Error details:', response?.error || 'No error details provided');
          loadingText.textContent = `Error loading opportunity details: ${response?.error || 'Unknown error'}`;

          // Add a retry button
          const retryBtn = document.createElement('button');
          retryBtn.textContent = 'Retry';
          retryBtn.style.cssText = `
            margin-top: 16px;
            padding: 8px 16px;
            background-color: #0a66c2;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
          `;
          retryBtn.addEventListener('click', () => {
            overlay.remove();
            showOpportunityPopup(opportunityId);
          });

          loadingPopup.appendChild(retryBtn);

          // Add a view in Affinity button
          const viewInAffinityBtn = document.createElement('a');
          viewInAffinityBtn.textContent = 'View in Affinity';
          viewInAffinityBtn.href = `https://bluewirecapital.affinity.co/opportunities/${opportunityId}`;
          viewInAffinityBtn.target = '_blank';
          viewInAffinityBtn.style.cssText = `
            display: inline-block;
            margin-top: 16px;
            margin-left: 16px;
            padding: 8px 16px;
            background-color: white;
            color: #0a66c2;
            border: 1px solid #0a66c2;
            border-radius: 4px;
            text-decoration: none;
            cursor: pointer;
          `;

          loadingPopup.appendChild(viewInAffinityBtn);
        }
      }
    );
  } catch (error) {
    console.error('[content.js] Exception when sending message:', error);
    loadingText.textContent = `Error: ${error.message || 'Unknown error'}`;

    // Add a view in Affinity button
    const viewInAffinityBtn = document.createElement('a');
    viewInAffinityBtn.textContent = 'View in Affinity';
    viewInAffinityBtn.href = `https://bluewirecapital.affinity.co/opportunities/${opportunityId}`;
    viewInAffinityBtn.target = '_blank';
    viewInAffinityBtn.style.cssText = `
      display: block;
      margin: 16px auto 0;
      padding: 8px 16px;
      background-color: white;
      color: #0a66c2;
      border: 1px solid #0a66c2;
      border-radius: 4px;
      text-decoration: none;
      cursor: pointer;
      width: fit-content;
    `;

    loadingPopup.appendChild(viewInAffinityBtn);
  }
}

/** 3.4) Display opportunity details in popup */
function displayOpportunityDetails(overlay, details, opportunityId) {
  // Remove any existing content
  overlay.innerHTML = '';

  // Create popup container
  const popup = document.createElement('div');
  popup.style.cssText = `
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    width: 500px;
    max-width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
  `;

  // Create header
  const header = document.createElement('div');
  header.style.cssText = `
    padding: 16px 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  `;

  const title = document.createElement('h2');
  title.textContent = details.name || 'Opportunity Details';
  title.style.cssText = `
    font-size: 18px;
    font-weight: 600;
    margin: 0;
  `;

  const closeBtn = document.createElement('button');
  closeBtn.innerHTML = '&times;';
  closeBtn.style.cssText = `
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #666;
  `;
  closeBtn.addEventListener('click', () => overlay.remove());

  header.appendChild(title);
  header.appendChild(closeBtn);

  // Create content
  const content = document.createElement('div');
  content.style.cssText = `
    padding: 20px;
  `;

  // Status dropdown
  // Store the raw response for later use
  const rawResponse = JSON.stringify(details);

  // Log the field values which contain the status
  console.log('DETAILED STATUS DEBUG INFO:');
  console.log('Status field ID:', details.statusField?.id);
  console.log('All field values:', details.fieldValues);

  if (details.fieldValues && Array.isArray(details.fieldValues)) {
    // Find the status field value
    const statusFieldValue = details.fieldValues.find(fv =>
      fv.field_id === details.statusField?.id
    );

    if (statusFieldValue) {
      console.log('Found status field value:', statusFieldValue);
      console.log('Status value type:', typeof statusFieldValue.value);
      console.log('Status value:', statusFieldValue.value);

      if (typeof statusFieldValue.value === 'object') {
        console.log('Status ID:', statusFieldValue.value.id);
        console.log('Status text:', statusFieldValue.value.text);
      }
    } else {
      console.log('Status field value NOT FOUND in field values');

      // Log all field IDs to help debug
      console.log('Available field IDs:', details.fieldValues.map(fv => fv.field_id));
    }
  } else {
    console.log('No field values found in response');
  }

  if (details.statusField && Array.isArray(details.statusOptions)) {
    const statusGroup = document.createElement('div');
    statusGroup.style.cssText = `margin-bottom: 16px;`;

    const statusLabel = document.createElement('label');
    statusLabel.textContent = 'Status:';
    statusLabel.style.cssText = `
      font-weight: 600;
      margin-bottom: 8px;
      display: block;
    `;
    statusLabel.setAttribute('for', 'bwc-status-select');

    const statusSelect = document.createElement('select');
    statusSelect.id = 'bwc-status-select';
    statusSelect.name = 'bwc-status-select';
    statusSelect.style.cssText = `
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      font-size: 14px;
      margin-bottom: 16px;
      height: 40px;
      color: #191919;
      background-color: #fff;
      box-shadow: 0 1px 3px rgba(0,0,0,0.08);
      box-sizing: border-box;
    `;

    // Add options from statusOptions
    if (details.statusOptions.length > 0) {
      console.log('Status details for dropdown:', {
        status: details.status,
        statusOptions: details.statusOptions.map(opt => ({ id: opt.id, text: opt.text }))
      });

      // Add all status options
      details.statusOptions.forEach(option => {
        const optEl = document.createElement('option');
        optEl.value = option.id;
        optEl.textContent = option.text;

        // Don't set any option as selected by default
        // We'll determine the current status later

        statusSelect.appendChild(optEl);
      });

      // Find and set the current status in the dropdown
      console.log('Setting up status dropdown with current status');

      // Don't select any option by default
      let currentStatusId = null;

      // Try to find the current status in fieldValues
      if (details.fieldValues && Array.isArray(details.fieldValues)) {
        // Find the field value that corresponds to the status field
        const statusFieldValue = details.fieldValues.find(fv =>
          String(fv.field_id) === String(details.statusField.id)
        );

        if (statusFieldValue && statusFieldValue.value) {
          console.log('Found status in fieldValues:', statusFieldValue.value);

          // The status value can be either an object with an id property or just an id
          currentStatusId = typeof statusFieldValue.value === 'object' ?
            statusFieldValue.value.id : statusFieldValue.value;

          console.log('Current status ID from fieldValues:', currentStatusId);
        } else {
          console.log('Status field value not found in fieldValues');
        }
      }

      // If we found a status ID, select it in the dropdown
      if (currentStatusId) {
        let statusFound = false;

        // Find the matching option in the dropdown
        for (let i = 0; i < statusSelect.options.length; i++) {
          if (String(statusSelect.options[i].value) === String(currentStatusId)) {
            statusSelect.options[i].selected = true;
            statusFound = true;
            console.log('Selected status in dropdown:', statusSelect.options[i].textContent);
            break;
          }
        }

        if (!statusFound) {
          console.log('Status ID not found in dropdown options:', currentStatusId);
          console.log('Available options:', Array.from(statusSelect.options).map(opt => ({
            value: opt.value,
            text: opt.textContent
          })));
        }
      } else {
        console.log('No current status ID found, not selecting any option');
      }

      // Log the final selected status
      const selectedOption = statusSelect.options[statusSelect.selectedIndex];
      console.log('Final selected status:', selectedOption ? selectedOption.textContent : 'none');
      console.log('Final selected status ID:', statusSelect.value);

      // Add a change event listener to log when the selection changes
      statusSelect.addEventListener('change', (e) => {
        console.log('Status selection changed to:', e.target.value,
                    'text:', e.target.options[e.target.selectedIndex].textContent);
      });
    } else {
      // If no options, add a placeholder
      const placeholderOpt = document.createElement('option');
      placeholderOpt.disabled = true;
      placeholderOpt.selected = true;
      placeholderOpt.textContent = 'No status options available';
      statusSelect.appendChild(placeholderOpt);
    }

    statusGroup.appendChild(statusLabel);
    statusGroup.appendChild(statusSelect);
    content.appendChild(statusGroup);

    // Log the select element and its options
    console.log('Status select element created:', statusSelect);
    console.log('Status select options:', Array.from(statusSelect.options).map(opt => ({ value: opt.value, text: opt.text, selected: opt.selected })));
  } else {
    console.log('Status field or options missing:', details);
  }

  // Notes section
  const notesGroup = document.createElement('div');
  notesGroup.style.cssText = `margin-bottom: 16px;`;

  const notesLabel = document.createElement('label');
  notesLabel.textContent = 'Notes:';
  notesLabel.style.cssText = `
    font-weight: 600;
    margin-bottom: 8px;
    display: block;
  `;

  const notesContainer = document.createElement('div');
  notesContainer.style.cssText = `margin-bottom: 16px;`;

  // Display existing notes
  console.log('Notes data:', details.notes);

  // Check if notes is an array or an object with a notes property
  let notesArray = [];
  if (details.notes) {
    if (Array.isArray(details.notes)) {
      notesArray = details.notes;
    } else if (details.notes.notes && Array.isArray(details.notes.notes)) {
      // Handle case where notes is an object with a notes property
      notesArray = details.notes.notes;
      console.log('Found notes array inside notes object:', notesArray);
    }
  }

  if (notesArray.length > 0) {
    // Sort notes by created_at in descending order (newest first)
    const sortedNotes = [...notesArray].sort((a, b) => {
      return new Date(b.created_at) - new Date(a.created_at);
    });

    let hasDisplayedNotes = false;

    sortedNotes.forEach(note => {
      if (note && note.content) {
        hasDisplayedNotes = true;
        const noteEl = document.createElement('div');
        noteEl.style.cssText = `
          background-color: #f5f5f5;
          border-radius: 4px;
          padding: 12px;
          margin-bottom: 8px;
        `;

        const noteDate = document.createElement('div');
        noteDate.style.cssText = `
          font-size: 12px;
          color: #666;
          margin-bottom: 4px;
        `;
        const date = new Date(note.created_at);
        noteDate.textContent = date.toLocaleString();

        const noteContent = document.createElement('div');
        noteContent.style.cssText = `white-space: pre-wrap;`;
        noteContent.textContent = note.content;

        noteEl.appendChild(noteDate);
        noteEl.appendChild(noteContent);
        notesContainer.appendChild(noteEl);
      }
    });

    if (!hasDisplayedNotes) {
      const noNotes = document.createElement('p');
      noNotes.textContent = 'No notes with content available.';
      noNotes.style.cssText = `
        color: #666;
        font-style: italic;
      `;
      notesContainer.appendChild(noNotes);
    }
  } else {
    const noNotes = document.createElement('p');
    noNotes.textContent = 'No notes available.';
    noNotes.style.cssText = `
      color: #666;
      font-style: italic;
    `;
    notesContainer.appendChild(noNotes);
  }

  // New note textarea
  const newNoteLabel = document.createElement('label');
  newNoteLabel.textContent = 'Add a note:';
  newNoteLabel.style.cssText = `
    font-weight: 600;
    margin-bottom: 8px;
    display: block;
  `;
  newNoteLabel.setAttribute('for', 'bwc-new-note');

  const newNoteTextarea = document.createElement('textarea');
  newNoteTextarea.id = 'bwc-new-note';
  newNoteTextarea.placeholder = 'Type your note here...';
  newNoteTextarea.style.cssText = `
    width: 100%;
    min-height: 80px;
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 14px;
    resize: vertical;
    color: #191919;
    box-shadow: 0 1px 3px rgba(0,0,0,0.08);
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  `;

  notesGroup.appendChild(notesLabel);
  notesGroup.appendChild(notesContainer);
  notesGroup.appendChild(newNoteLabel);
  notesGroup.appendChild(newNoteTextarea);

  content.appendChild(notesGroup);

  // Create footer with buttons
  const footer = document.createElement('div');
  footer.style.cssText = `
    padding: 16px 20px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  `;

  // Create "View in Affinity" button
  const viewBtn = document.createElement('a');
  viewBtn.textContent = 'View in Affinity';
  viewBtn.href = `https://bluewirecapital.affinity.co/opportunities/${opportunityId}`;
  viewBtn.target = '_blank';
  viewBtn.rel = 'noopener noreferrer';
  viewBtn.style.cssText = `
    background-color: #ffffff;
    color: #0a66c2;
    border: 1px solid #0a66c2;
    border-radius: 24px;
    padding: 8px 16px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: background-color 0.2s;
    height: 36px;
    box-sizing: border-box;
  `;

  // Add hover effects
  viewBtn.addEventListener('mouseover', () => {
    viewBtn.style.backgroundColor = '#f5f9ff';
  });

  viewBtn.addEventListener('mouseout', () => {
    viewBtn.style.backgroundColor = 'white';
  });

  // Create right side container for status message and update button
  const footerRight = document.createElement('div');
  footerRight.style.cssText = `
    display: flex;
    align-items: center;
  `;

  const statusMessage = document.createElement('div');
  statusMessage.id = 'bwc-status-message';
  statusMessage.style.cssText = `
    margin-right: 12px;
    font-size: 14px;
    color: #666;
    align-self: center;
  `;

  const updateBtn = document.createElement('button');
  updateBtn.textContent = 'Update';
  updateBtn.style.cssText = `
    background-color: #0a66c2;
    color: white;
    border: none;
    border-radius: 24px;
    padding: 8px 16px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s;
    height: 36px;
    box-sizing: border-box;
  `;

  // Add hover effects
  updateBtn.addEventListener('mouseover', () => {
    updateBtn.style.backgroundColor = '#004182';
  });

  updateBtn.addEventListener('mouseout', () => {
    updateBtn.style.backgroundColor = '#0a66c2';
  });

  updateBtn.addEventListener('click', () => {
    // Get the status select element at the time of the click
    const statusSelectElement = document.getElementById('bwc-status-select');

    console.log('Status select element:', statusSelectElement);
    console.log('Status select value:', statusSelectElement ? statusSelectElement.value : 'not found');

    // Get all the values we need
    const newStatusId = statusSelectElement ? statusSelectElement.value : null;
    // We'll determine the actual old status in handleUpdate
    const oldStatusId = null; // This will be set in handleUpdate
    const newNoteText = newNoteTextarea.value;

    console.log('Update button clicked with values:', {
      opportunityId,
      listEntryId: details.listEntryId,
      statusFieldId: details.statusField?.id,
      newStatusId,
      oldStatusId,
      newNote: newNoteText
    });

    handleUpdate(
      opportunityId,
      details.listEntryId,
      details.statusField?.id,
      newStatusId,
      oldStatusId,
      newNoteText,
      statusMessage,
      updateBtn,
      overlay
    );
  });

  footerRight.appendChild(statusMessage);
  footerRight.appendChild(updateBtn);

  footer.appendChild(viewBtn);
  footer.appendChild(footerRight);

  // Assemble popup
  popup.appendChild(header);
  popup.appendChild(content);
  popup.appendChild(footer);
  overlay.appendChild(popup);
}

/** 3.5) Handle update button click */
async function handleUpdate(
  opportunityId,
  listEntryId,
  statusFieldId,
  newStatusId,
  oldStatusId,
  newNote,
  statusMessage,
  updateBtn,
  overlay
) {
  console.log('handleUpdate called with:', {
    opportunityId,
    listEntryId,
    statusFieldId,
    newStatusId,
    oldStatusId,
    newNote
  });

  // Check if anything has changed
  console.log('Status values for change detection:', { newStatusId, oldStatusId });

  // Get the current status from the dropdown
  const statusSelect = document.getElementById('bwc-status-select');
  const currentStatusId = statusSelect ? statusSelect.value : null;

  // Use the current status from the dropdown as the old status
  // This ensures we have the correct old status even if it wasn't in the API response
  oldStatusId = currentStatusId;

  // Always consider it a status change if newStatusId is set
  // This ensures we always update the status when the user clicks the update button
  const statusChanged = true; // Always update the status
  console.log('Status will be updated:', statusChanged);
  console.log('New status ID:', newStatusId);
  console.log('Old status ID (from dropdown):', oldStatusId);
  const hasNewNote = newNote && newNote.trim().length > 0;

  console.log('Change detection:', { statusChanged, hasNewNote, currentStatusId });

  if (!statusChanged && !hasNewNote) {
    statusMessage.textContent = 'No changes have been made';
    return;
  }

  // Validate required fields
  if (statusChanged && (!statusFieldId || !listEntryId)) {
    console.error('Missing required fields for status update:', { statusFieldId, listEntryId });
    statusMessage.textContent = 'Error: Missing required fields for status update';
    return;
  }

  // Disable button and show loading state
  updateBtn.disabled = true;
  updateBtn.textContent = 'Updating...';
  statusMessage.textContent = '';

  try {
    // If we only have a note to add, we can do it directly here
    if (hasNewNote && !statusChanged) {
      console.log('Adding note directly');

      try {
        // Get the API key
        const apiKeyResponse = await new Promise(resolve => {
          chrome.runtime.sendMessage({ type: 'getApiKey' }, resolve);
        });

        if (!apiKeyResponse || !apiKeyResponse.apiKey) {
          throw new Error('No API key available');
        }

        const apiKey = apiKeyResponse.apiKey;

        // Add the note directly
        const noteResponse = await fetch(`https://api.affinity.co/notes`, {
          method: 'POST',
          headers: {
            "Authorization": "Basic " + btoa(":" + apiKey),
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            content: newNote,
            opportunity_ids: [parseInt(opportunityId, 10)],
            type: 2 // Regular note type
          })
        });

        if (!noteResponse.ok) {
          const errorText = await noteResponse.text();
          throw new Error(`Failed to add note: ${noteResponse.status} - ${errorText}`);
        }

        // Show success message
        statusMessage.textContent = 'Note added successfully!';
        updateBtn.textContent = 'Update';
        updateBtn.disabled = false;

        // Refresh the popup after a short delay
        setTimeout(() => {
          overlay.remove();
          showOpportunityPopup(opportunityId);
        }, 1000);

        return;
      } catch (directError) {
        console.error('Error adding note directly:', directError);
        // Fall back to using the background script
      }
    }

    // Send update request to background script
    console.log('Sending update request to background script');

    // Always force statusChanged to true to ensure the status is updated
    const forceStatusChanged = true;

    const response = await new Promise((resolve) => {
      chrome.runtime.sendMessage(
        {
          type: 'updateOpportunity',
          opportunityId,
          listEntryId,
          statusFieldId,
          newStatusId,
          oldStatusId,
          newNote,
          statusChanged: forceStatusChanged,
          hasNewNote
        },
        (result) => {
          console.log('Update response received:', result);
          resolve(result);
        }
      );
    });

    if (response && response.success) {
      // Show success message
      statusMessage.textContent = 'Updated successfully!';
      updateBtn.textContent = 'Update';
      updateBtn.disabled = false;

      // Refresh the popup after a short delay
      setTimeout(() => {
        // Remove the current popup
        overlay.remove();

        // Show a new popup with updated data
        showOpportunityPopup(opportunityId);
      }, 1000);
    } else {
      // Show error message
      console.error('[content.js] Update error:', response?.error);
      statusMessage.textContent = `Error: ${response?.error || 'Unknown error'}`;
      updateBtn.textContent = 'Update';
      updateBtn.disabled = false;
    }
  } catch (error) {
    console.error('[content.js] Exception in handleUpdate:', error);
    statusMessage.textContent = `Error: ${error.message || 'Unknown error'}`;
    updateBtn.textContent = 'Update';
    updateBtn.disabled = false;
  }
}

/** 3.6) LinkedIn Profile Scraping Functions */
async function scrapeLinkedInProfile() {
  console.log('[content.js] Starting enhanced LinkedIn profile scraping...');

  // Enhanced waiting for dynamic content
  await waitForContentToLoad();

  // Comprehensive page structure analysis
  const pageStructure = await analyzePageStructure();
  console.log('[content.js] Page structure analysis completed');

  const profileData = {
    name: '',
    headline: '',
    location: '',
    about: '',
    experience: [],
    education: [],
    skills: [],
    languages: [],
    certifications: [],
    contact: {},
    profileUrl: window.location.href,
    scrapedAt: new Date().toISOString(),
    debug: {
      pageStructure: pageStructure,
      extractionAttempts: {}
    }
  };

  try {
    // Get name from h1
    profileData.name = getProfileName();
    console.log('[content.js] Name extracted:', profileData.name);

    // Enhanced headline extraction
    profileData.headline = await extractHeadline();
    console.log('[content.js] Headline extracted:', profileData.headline);

    // Enhanced location extraction
    profileData.location = await extractLocation();
    console.log('[content.js] Location extracted:', profileData.location);

    // Enhanced about section extraction
    profileData.about = await extractAboutSection();
    console.log('[content.js] About section extracted, length:', profileData.about.length);

    // Contact information extraction
    profileData.contact = await extractContactInfo();
    console.log('[content.js] Contact info extracted:', profileData.contact);

    // Get experience
    const experienceSection = document.querySelector('#experience');
    if (experienceSection) {
      const experienceItems = experienceSection.closest('section')?.querySelectorAll('.pvs-list__paged-list-item, .artdeco-list__item');
      if (experienceItems) {
        experienceItems.forEach(item => {
          const experience = extractExperienceItem(item);
          if (experience.title) {
            profileData.experience.push(experience);
          }
        });
      }
    }

    // Get education
    const educationSection = document.querySelector('#education');
    if (educationSection) {
      const educationItems = educationSection.closest('section')?.querySelectorAll('.pvs-list__paged-list-item, .artdeco-list__item');
      if (educationItems) {
        educationItems.forEach(item => {
          const education = extractEducationItem(item);
          if (education.school) {
            profileData.education.push(education);
          }
        });
      }
    }

    // Get skills
    const skillsSection = document.querySelector('#skills');
    if (skillsSection) {
      const skillItems = skillsSection.closest('section')?.querySelectorAll('.pvs-list__paged-list-item .mr1.hoverable-link-text.t-bold span[aria-hidden="true"], .skill-category-entity__name .visually-hidden');
      if (skillItems) {
        skillItems.forEach(item => {
          const skill = item.textContent.trim();
          if (skill && !profileData.skills.includes(skill)) {
            profileData.skills.push(skill);
          }
        });
      }
    }

    // Get languages
    const languagesSection = document.querySelector('#languages');
    if (languagesSection) {
      const languageItems = languagesSection.closest('section')?.querySelectorAll('.pvs-list__paged-list-item');
      if (languageItems) {
        languageItems.forEach(item => {
          const language = extractLanguageItem(item);
          if (language.name) {
            profileData.languages.push(language);
          }
        });
      }
    }

    // Enhanced experience extraction
    profileData.experience = await extractExperience();
    console.log('[content.js] Experience extracted:', profileData.experience.length, 'items');

    // Enhanced education extraction
    profileData.education = await extractEducation();
    console.log('[content.js] Education extracted:', profileData.education.length, 'items');

    // Enhanced skills extraction
    profileData.skills = await extractSkills();
    console.log('[content.js] Skills extracted:', profileData.skills.length, 'items');

    // Enhanced languages extraction
    profileData.languages = await extractLanguages();
    console.log('[content.js] Languages extracted:', profileData.languages.length, 'items');

    // Enhanced certifications extraction
    profileData.certifications = await extractCertifications();
    console.log('[content.js] Certifications extracted:', profileData.certifications.length, 'items');

    console.log('[content.js] Enhanced profile scraping completed:', profileData);
    return profileData;

  } catch (error) {
    console.error('[content.js] Error scraping profile:', error);
    profileData.debug.error = error.message;
    return profileData; // Return partial data instead of throwing
  }
}

// Enhanced helper functions for content loading and extraction
async function waitForContentToLoad() {
  console.log('[content.js] Waiting for content to load...');

  // Wait for initial page load
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Wait for main content sections to appear
  let attempts = 0;
  const maxAttempts = 20;

  while (attempts < maxAttempts) {
    const mainSections = document.querySelectorAll('main section');
    if (mainSections.length >= 3) {
      console.log('[content.js] Main sections loaded:', mainSections.length);
      break;
    }

    await new Promise(resolve => setTimeout(resolve, 500));
    attempts++;
  }

  // Additional wait for dynamic content
  await new Promise(resolve => setTimeout(resolve, 1000));
  console.log('[content.js] Content loading wait completed');
}

async function analyzePageStructure() {
  console.log('[content.js] Analyzing page structure...');

  const structure = {
    totalSections: 0,
    sectionsWithIds: [],
    aboutSectionFound: false,
    experienceSectionFound: false,
    educationSectionFound: false,
    skillsSectionFound: false,
    potentialAboutCandidates: []
  };

  const sections = document.querySelectorAll('section');
  structure.totalSections = sections.length;

  sections.forEach((section, index) => {
    if (section.id) {
      structure.sectionsWithIds.push({
        id: section.id,
        index: index,
        textLength: section.textContent.trim().length
      });
    }

    const sectionText = section.textContent.toLowerCase();
    if (sectionText.includes('about') && sectionText.length > 100) {
      structure.potentialAboutCandidates.push({
        index: index,
        id: section.id || 'no-id',
        textLength: section.textContent.trim().length,
        preview: section.textContent.trim().substring(0, 200)
      });
    }
  });

  // Check for specific sections
  structure.aboutSectionFound = !!document.querySelector('#about');
  structure.experienceSectionFound = !!document.querySelector('#experience');
  structure.educationSectionFound = !!document.querySelector('#education');
  structure.skillsSectionFound = !!document.querySelector('#skills');

  console.log('[content.js] Page structure analysis:', structure);
  return structure;
}

async function extractHeadline() {
  console.log('[content.js] Extracting headline...');

  const headlineSelectors = [
    'div.text-body-medium.break-words',
    '.pv-text-details__left-panel h2',
    '.text-body-medium.break-words',
    '.pv-text-details__left-panel .text-body-medium',
    '[data-generated-suggestion-target]',
    '.pv-top-card--list-bullet .pv-top-card--list-bullet-v2',
    'h2.pv-top-card--headline',
    '.pv-top-card .pv-top-card--headline',
    '.pv-text-details__left-panel .text-body-medium.break-words'
  ];

  for (const selector of headlineSelectors) {
    const elements = document.querySelectorAll(selector);
    for (const element of elements) {
      const text = element.textContent.trim();
      if (text && text.length > 10 && text.length < 200) {
        // Make sure it's not the name
        const name = getProfileName();
        if (text !== name && !text.includes('connections') && !text.includes('followers')) {
          console.log('[content.js] Headline found with selector:', selector, '- Text:', text);
          return text;
        }
      }
    }
  }

  console.log('[content.js] No headline found');
  return '';
}

async function extractLocation() {
  console.log('[content.js] Extracting location...');

  const locationSelectors = [
    '.pv-text-details__left-panel .text-body-small.inline.t-black--light.break-words',
    '.pb2.pv-text-details__left-panel .text-body-small',
    '.pv-text-details__left-panel .text-body-small',
    '.pv-top-card--list .pv-top-card--list-bullet',
    '.pv-top-card--list-bullet .text-body-small',
    '[data-field="location_in_profile"]',
    '.pv-text-details__left-panel .text-body-small.inline',
    '.pv-top-card .text-body-small.inline',
    '.pv-top-card--list-bullet',
    '.pv-text-details__left-panel span.text-body-small'
  ];

  for (const selector of locationSelectors) {
    const elements = document.querySelectorAll(selector);
    for (const element of elements) {
      const text = element.textContent.trim();
      if (text && text.length > 2 && text.length < 100) {
        // Check if it looks like a location (contains common location indicators)
        const locationIndicators = [',', 'United States', 'United Kingdom', 'Canada', 'Australia', 'Germany', 'France', 'Area', 'Metropolitan', 'Greater'];
        const hasLocationIndicator = locationIndicators.some(indicator => text.includes(indicator));

        // Or if it's a reasonable length and doesn't contain obvious non-location text
        const isNotOtherInfo = !text.includes('connections') &&
                              !text.includes('followers') &&
                              !text.includes('experience') &&
                              !text.includes('years') &&
                              !text.includes('@') &&
                              !text.includes('http');

        if (hasLocationIndicator || (isNotOtherInfo && text.length > 5)) {
          console.log('[content.js] Location found with selector:', selector, '- Text:', text);
          return text;
        }
      }
    }
  }

  console.log('[content.js] No location found');
  return '';
}

async function extractAboutSection() {
  console.log('[content.js] Extracting about section...');

  // Strategy 1: Look for #about anchor
  let aboutSection = document.querySelector('#about');
  if (aboutSection) {
    console.log('[content.js] Found #about anchor');
    const aboutContainer = aboutSection.closest('section');
    if (aboutContainer) {
      const aboutText = await extractTextFromAboutContainer(aboutContainer);
      if (aboutText && aboutText.length > 50) {
        console.log('[content.js] About text extracted via #about anchor');
        return aboutText;
      }
    }
  }

  // Strategy 2: Look for sections containing "About" in header
  const sections = document.querySelectorAll('section');
  for (const section of sections) {
    const headerElements = section.querySelectorAll('h2, h3, .text-heading-large, .text-heading-medium');
    for (const header of headerElements) {
      if (header.textContent.toLowerCase().includes('about')) {
        console.log('[content.js] Found section with "About" header');
        const aboutText = await extractTextFromAboutContainer(section);
        if (aboutText && aboutText.length > 50) {
          console.log('[content.js] About text extracted via header search');
          return aboutText;
        }
      }
    }
  }

  // Strategy 3: Look for substantial text blocks that seem like about content
  const allTextElements = document.querySelectorAll('div, span, p');
  const aboutCandidates = [];

  for (const element of allTextElements) {
    const text = element.textContent.trim();
    if (text.length > 100 && text.length < 2000) {
      // Check for about-like keywords
      const aboutKeywords = ['passionate', 'experience', 'background', 'expertise', 'focus', 'specialize', 'dedicated', 'committed', 'professional', 'career'];
      const keywordCount = aboutKeywords.filter(keyword => text.toLowerCase().includes(keyword)).length;

      // Make sure it's not part of other sections
      const isNotOtherSection = !text.toLowerCase().includes('company:') &&
                               !text.toLowerCase().includes('duration:') &&
                               !text.toLowerCase().includes('university') &&
                               !element.closest('[id*="experience"]') &&
                               !element.closest('[id*="education"]') &&
                               !element.closest('[id*="skills"]');

      if (keywordCount >= 2 && isNotOtherSection) {
        aboutCandidates.push({
          element: element,
          text: text,
          keywordCount: keywordCount,
          score: keywordCount * (text.length / 100) // Score based on keywords and length
        });
      }
    }
  }

  // Sort by score and return the best candidate
  if (aboutCandidates.length > 0) {
    aboutCandidates.sort((a, b) => b.score - a.score);
    const bestCandidate = aboutCandidates[0];
    console.log('[content.js] About text extracted via pattern matching, score:', bestCandidate.score);
    return bestCandidate.text;
  }

  console.log('[content.js] No about section found');
  return '';
}

async function extractContactInfo() {
  console.log('[content.js] Extracting contact information...');

  const contact = {
    email: '',
    phone: '',
    website: '',
    linkedin: window.location.href
  };

  // Look for contact information in various places
  const contactSelectors = [
    'a[href^="mailto:"]',
    'a[href^="tel:"]',
    'a[href^="http"]:not([href*="linkedin.com"])'
  ];

  contactSelectors.forEach(selector => {
    const elements = document.querySelectorAll(selector);
    elements.forEach(element => {
      const href = element.href;
      if (href.startsWith('mailto:')) {
        contact.email = href.replace('mailto:', '');
      } else if (href.startsWith('tel:')) {
        contact.phone = href.replace('tel:', '');
      } else if (href.startsWith('http') && !href.includes('linkedin.com')) {
        contact.website = href;
      }
    });
  });

  return contact;
}

async function extractExperience() {
  console.log('[content.js] Extracting experience...');

  const experience = [];
  const experienceSection = document.querySelector('#experience');

  if (experienceSection) {
    const experienceItems = experienceSection.closest('section')?.querySelectorAll('.pvs-list__paged-list-item, .artdeco-list__item');
    if (experienceItems) {
      experienceItems.forEach(item => {
        const exp = extractExperienceItem(item);
        if (exp.title) {
          experience.push(exp);
        }
      });
    }
  }

  return experience;
}

async function extractEducation() {
  console.log('[content.js] Extracting education...');

  const education = [];
  const educationSection = document.querySelector('#education');

  if (educationSection) {
    const educationItems = educationSection.closest('section')?.querySelectorAll('.pvs-list__paged-list-item, .artdeco-list__item');
    if (educationItems) {
      educationItems.forEach(item => {
        const edu = extractEducationItem(item);
        if (edu.school) {
          education.push(edu);
        }
      });
    }
  }

  return education;
}

async function extractSkills() {
  console.log('[content.js] Extracting skills...');

  const skills = [];
  const skillsSection = document.querySelector('#skills');

  if (skillsSection) {
    // Try multiple selectors for skills
    const skillSelectors = [
      '.pvs-list__paged-list-item .mr1.hoverable-link-text.t-bold span[aria-hidden="true"]',
      '.skill-category-entity__name .visually-hidden',
      '.pvs-list__paged-list-item .t-bold span[aria-hidden="true"]',
      '.pvs-list__paged-list-item .hoverable-link-text span'
    ];

    for (const selector of skillSelectors) {
      const skillItems = skillsSection.closest('section')?.querySelectorAll(selector);
      if (skillItems && skillItems.length > 0) {
        skillItems.forEach(item => {
          const skill = item.textContent.trim();
          if (skill && !skills.includes(skill)) {
            skills.push(skill);
          }
        });
        break; // If we found skills with this selector, don't try others
      }
    }
  }

  return skills;
}

async function extractLanguages() {
  console.log('[content.js] Extracting languages...');

  const languages = [];
  const languagesSection = document.querySelector('#languages');

  if (languagesSection) {
    const languageItems = languagesSection.closest('section')?.querySelectorAll('.pvs-list__paged-list-item');
    if (languageItems) {
      languageItems.forEach(item => {
        const language = extractLanguageItem(item);
        if (language.name) {
          languages.push(language);
        }
      });
    }
  }

  return languages;
}

async function extractCertifications() {
  console.log('[content.js] Extracting certifications...');

  const certifications = [];
  const certificationsSection = document.querySelector('#licenses_and_certifications');

  if (certificationsSection) {
    const certificationItems = certificationsSection.closest('section')?.querySelectorAll('.pvs-list__paged-list-item');
    if (certificationItems) {
      certificationItems.forEach(item => {
        const certification = extractCertificationItem(item);
        if (certification.name) {
          certifications.push(certification);
        }
      });
    }
  }

  return certifications;
}

function extractExperienceItem(item) {
  const experience = {
    title: '',
    company: '',
    duration: '',
    location: '',
    description: ''
  };

  try {
    // Get title
    const titleElement = item.querySelector('.mr1.hoverable-link-text.t-bold span[aria-hidden="true"], .t-bold span[aria-hidden="true"]');
    if (titleElement) {
      experience.title = titleElement.textContent.trim();
    }

    // Get company
    const companyElement = item.querySelector('.t-14.t-normal span[aria-hidden="true"]:not(.t-bold span), .pv-entity__secondary-title');
    if (companyElement) {
      experience.company = companyElement.textContent.trim();
    }

    // Get duration
    const durationElement = item.querySelector('.t-14.t-normal.t-black--light span[aria-hidden="true"], .pv-entity__bullet-item-v2');
    if (durationElement) {
      experience.duration = durationElement.textContent.trim();
    }

    // Get location
    const locationElement = item.querySelector('.t-14.t-normal.t-black--light span[aria-hidden="true"]:last-child');
    if (locationElement && locationElement.textContent !== experience.duration) {
      experience.location = locationElement.textContent.trim();
    }

    // Enhanced description extraction with dynamic analysis
    experience.description = extractDescriptionFromItem(item, {
      title: experience.title,
      company: experience.company,
      duration: experience.duration,
      location: experience.location
    });

  } catch (error) {
    console.error('[content.js] Error extracting experience item:', error);
  }

  return experience;
}

function extractDescriptionFromItem(item, knownFields) {
  console.log('[content.js] Extracting description from item...');

  // Strategy 1: Try specific description selectors
  const descriptionSelectors = [
    '.pv-shared-text-with-see-more .inline-show-more-text',
    '.pv-shared-text-with-see-more span[aria-hidden="true"]',
    '.pv-shared-text-with-see-more .visually-hidden',
    '.inline-show-more-text span[aria-hidden="true"]',
    '.pv-shared-text-with-see-more',
    '.inline-show-more-text',
    '.pvs-list__outer-container .visually-hidden',
    '.pvs-list__outer-container span[aria-hidden="true"]',
    '.pv-entity__extra-details .pv-shared-text-with-see-more',
    '.pv-entity__extra-details span[aria-hidden="true"]',
    '.pvs-list__item-content-container .visually-hidden',
    '.pvs-list__item-content-container span[aria-hidden="true"]',
    '.pv-entity__summary-info .pv-shared-text-with-see-more',
    '.pv-entity__summary-info span[aria-hidden="true"]'
  ];

  for (const selector of descriptionSelectors) {
    const elements = item.querySelectorAll(selector);
    for (const element of elements) {
      const text = element.textContent.trim();
      if (isValidDescription(text, knownFields)) {
        console.log('[content.js] Found description with selector:', selector);
        return text;
      }
    }
  }

  // Strategy 2: Look for elements with specific attributes that often contain descriptions
  const attributeSelectors = [
    '[aria-hidden="true"]',
    '.visually-hidden',
    '[data-field="description"]',
    '[data-field="summary"]'
  ];

  for (const selector of attributeSelectors) {
    const elements = item.querySelectorAll(selector);
    for (const element of elements) {
      const text = element.textContent.trim();
      if (isValidDescription(text, knownFields)) {
        console.log('[content.js] Found description with attribute selector:', selector);
        return text;
      }
    }
  }

  // Strategy 3: Analyze all text elements and score them
  const allElements = item.querySelectorAll('*');
  const textCandidates = [];

  allElements.forEach(element => {
    const text = element.textContent.trim();
    const directText = Array.from(element.childNodes)
      .filter(node => node.nodeType === Node.TEXT_NODE)
      .map(node => node.textContent.trim())
      .join(' ')
      .trim();

    // Check both full text and direct text
    [text, directText].forEach(candidateText => {
      if (candidateText && isValidDescription(candidateText, knownFields)) {
        const score = scoreDescriptionCandidate(candidateText, knownFields, element);
        if (score > 0) {
          textCandidates.push({
            text: candidateText,
            score: score,
            element: element,
            selector: getElementSelector(element)
          });
        }
      }
    });
  });

  // Sort by score and return the best candidate
  if (textCandidates.length > 0) {
    textCandidates.sort((a, b) => b.score - a.score);
    const bestCandidate = textCandidates[0];
    console.log('[content.js] Found description via scoring, score:', bestCandidate.score, 'selector:', bestCandidate.selector);
    return bestCandidate.text;
  }

  // Strategy 4: Last resort - look for any substantial text that's not already captured
  const allSpans = item.querySelectorAll('span, div, p');
  const lastResortCandidates = [];

  allSpans.forEach(element => {
    const text = element.textContent.trim();
    if (text.length > 30 &&
        text.length < 2000 &&
        !isKnownField(text, knownFields) &&
        !isUIText(text)) {
      lastResortCandidates.push(text);
    }
  });

  if (lastResortCandidates.length > 0) {
    // Return the longest text as most likely to be description
    lastResortCandidates.sort((a, b) => b.length - a.length);
    console.log('[content.js] Found description via last resort');
    return lastResortCandidates[0];
  }

  console.log('[content.js] No description found');
  return '';
}

function isValidDescription(text, knownFields) {
  if (!text || text.length < 20 || text.length > 5000) return false;

  // Check if it's a known field
  if (isKnownField(text, knownFields)) return false;

  // Check if it's UI text
  if (isUIText(text)) return false;

  // Check if it looks like a description (has descriptive words)
  const descriptiveWords = ['responsible', 'managed', 'developed', 'led', 'created', 'implemented', 'designed', 'coordinated', 'analyzed', 'improved', 'achieved', 'delivered', 'collaborated', 'established', 'maintained', 'supervised', 'executed', 'optimized', 'strategic', 'innovative'];
  const hasDescriptiveWords = descriptiveWords.some(word => text.toLowerCase().includes(word));

  // Or if it's substantial text that could be a description
  const isSubstantialText = text.length > 50 && text.includes(' ') && !text.includes('•');

  return hasDescriptiveWords || isSubstantialText;
}

function isKnownField(text, knownFields) {
  const fieldsToCheck = [knownFields.title, knownFields.company, knownFields.duration, knownFields.location];
  return fieldsToCheck.some(field => field && (text === field || text.includes(field) || field.includes(text)));
}

function isUIText(text) {
  const uiTexts = [
    'show all', 'show less', 'see more', 'see less', 'show more', 'hide',
    'connect', 'message', 'follow', 'unfollow', 'like', 'comment', 'share',
    'connections', 'followers', 'following', 'mutual', 'view profile',
    'send message', 'more actions', 'report', 'block', 'save',
    '·', '•', 'ago', 'week', 'month', 'year', 'day', 'hr', 'min',
    'full-time', 'part-time', 'contract', 'freelance', 'internship',
    'remote', 'on-site', 'hybrid'
  ];

  const lowerText = text.toLowerCase();
  return uiTexts.some(uiText => lowerText.includes(uiText)) ||
         /^\d+\s*(year|month|week|day|hr|min)s?\s*(ago)?$/i.test(text) ||
         /^\d+\s*-\s*\d+\s*(year|month)s?$/i.test(text);
}

function scoreDescriptionCandidate(text, knownFields, element) {
  let score = 0;

  // Length scoring (sweet spot around 100-500 characters)
  if (text.length >= 50 && text.length <= 1000) {
    score += Math.min(text.length / 10, 50);
  }

  // Descriptive words bonus
  const descriptiveWords = ['responsible', 'managed', 'developed', 'led', 'created', 'implemented', 'designed', 'coordinated', 'analyzed', 'improved', 'achieved', 'delivered', 'collaborated', 'established', 'maintained', 'supervised', 'executed', 'optimized'];
  const descriptiveWordCount = descriptiveWords.filter(word => text.toLowerCase().includes(word)).length;
  score += descriptiveWordCount * 10;

  // Sentence structure bonus
  const sentenceCount = text.split(/[.!?]+/).filter(s => s.trim().length > 0).length;
  if (sentenceCount >= 2) score += 20;

  // Element context bonus
  const elementClasses = element.className.toLowerCase();
  if (elementClasses.includes('description') ||
      elementClasses.includes('summary') ||
      elementClasses.includes('details')) {
    score += 30;
  }

  // Penalty for very short or very long text
  if (text.length < 30) score -= 20;
  if (text.length > 1500) score -= 10;

  return score;
}

function getElementSelector(element) {
  if (element.id) return `#${element.id}`;
  if (element.className) return `.${element.className.split(' ').join('.')}`;
  return element.tagName.toLowerCase();
}

function extractEducationItem(item) {
  const education = {
    school: '',
    degree: '',
    field: '',
    duration: '',
    description: ''
  };

  try {
    // Get school name
    const schoolElement = item.querySelector('.mr1.hoverable-link-text.t-bold span[aria-hidden="true"], .t-bold span[aria-hidden="true"]');
    if (schoolElement) {
      education.school = schoolElement.textContent.trim();
    }

    // Get degree and field
    const degreeElement = item.querySelector('.t-14.t-normal span[aria-hidden="true"]');
    if (degreeElement) {
      const degreeText = degreeElement.textContent.trim();
      // Try to split degree and field if they're combined
      if (degreeText.includes(',')) {
        const parts = degreeText.split(',');
        education.degree = parts[0].trim();
        education.field = parts[1].trim();
      } else {
        education.degree = degreeText;
      }
    }

    // Get duration
    const durationElement = item.querySelector('.t-14.t-normal.t-black--light span[aria-hidden="true"]');
    if (durationElement) {
      education.duration = durationElement.textContent.trim();
    }

    // Enhanced description extraction with dynamic analysis
    education.description = extractDescriptionFromItem(item, {
      title: education.school,
      company: education.degree,
      duration: education.duration,
      location: education.field
    });

  } catch (error) {
    console.error('[content.js] Error extracting education item:', error);
  }

  return education;
}

function extractLanguageItem(item) {
  const language = {
    name: '',
    proficiency: ''
  };

  try {
    // Get language name
    const nameElement = item.querySelector('.mr1.hoverable-link-text.t-bold span[aria-hidden="true"], .t-bold span[aria-hidden="true"]');
    if (nameElement) {
      language.name = nameElement.textContent.trim();
    }

    // Get proficiency
    const proficiencyElement = item.querySelector('.t-14.t-normal span[aria-hidden="true"]');
    if (proficiencyElement) {
      language.proficiency = proficiencyElement.textContent.trim();
    }

  } catch (error) {
    console.error('[content.js] Error extracting language item:', error);
  }

  return language;
}

function extractCertificationItem(item) {
  const certification = {
    name: '',
    issuer: '',
    date: '',
    credentialId: '',
    url: ''
  };

  try {
    // Get certification name
    const nameElement = item.querySelector('.mr1.hoverable-link-text.t-bold span[aria-hidden="true"], .t-bold span[aria-hidden="true"]');
    if (nameElement) {
      certification.name = nameElement.textContent.trim();
    }

    // Get issuer
    const issuerElement = item.querySelector('.t-14.t-normal span[aria-hidden="true"]');
    if (issuerElement) {
      certification.issuer = issuerElement.textContent.trim();
    }

    // Get date
    const dateElement = item.querySelector('.t-14.t-normal.t-black--light span[aria-hidden="true"]');
    if (dateElement) {
      certification.date = dateElement.textContent.trim();
    }

    // Get credential URL if available
    const linkElement = item.querySelector('a[href*="credential"]');
    if (linkElement) {
      certification.url = linkElement.href;
    }

  } catch (error) {
    console.error('[content.js] Error extracting certification item:', error);
  }

  return certification;
}

async function copyToClipboard(profileData) {
  const formattedData = formatProfileData(profileData);

  try {
    await navigator.clipboard.writeText(formattedData);
    console.log('[content.js] Profile data copied to clipboard');
  } catch (error) {
    console.error('[content.js] Failed to copy to clipboard:', error);
    // Fallback method
    const textArea = document.createElement('textarea');
    textArea.value = formattedData;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
  }
}

/** Extract first name from full name with title handling */
function extractFirstName(fullName) {
  if (!fullName || typeof fullName !== 'string') {
    return '';
  }

  const words = fullName.trim().split(/\s+/);
  if (words.length === 0) {
    return '';
  }

  // List of common titles to skip
  const titles = ['dr', 'dr.', 'prof', 'prof.', 'mr', 'mr.', 'mrs', 'mrs.', 'ms', 'ms.', 'miss'];

  // Check if first word is a title
  const firstWord = words[0].toLowerCase();
  if (titles.includes(firstWord) && words.length > 1) {
    return words[1]; // Return second word if first is a title
  }

  return words[0]; // Return first word otherwise
}



function formatProfileData(data) {
  let formatted = `LINKEDIN PROFILE DATA\n`;
  formatted += `===================\n\n`;

  formatted += `Name: ${data.name}\n`;
  formatted += `Headline: ${data.headline}\n\n`;

  // Contact Information
  if (data.contact && (data.contact.email || data.contact.phone || data.contact.website)) {
    formatted += `CONTACT INFORMATION\n`;
    formatted += `-------------------\n`;
    if (data.contact.email) formatted += `Email: ${data.contact.email}\n`;
    if (data.contact.phone) formatted += `Phone: ${data.contact.phone}\n`;
    if (data.contact.website) formatted += `Website: ${data.contact.website}\n`;
    formatted += `\n`;
  }

  if (data.about && data.about.length > 0) {
    formatted += `ABOUT\n-----\n${data.about}\n\n`;
  } else {
    formatted += `ABOUT\n-----\n(No about section found)\n\n`;
  }

  if (data.experience.length > 0) {
    formatted += `EXPERIENCE\n----------\n`;
    data.experience.forEach((exp, index) => {
      formatted += `${index + 1}. ${exp.title}\n`;
      if (exp.company) formatted += `   Company: ${exp.company}\n`;
      if (exp.description) formatted += `   Description: ${exp.description}\n`;
      formatted += `\n`;
    });
  }

  if (data.education.length > 0) {
    formatted += `EDUCATION\n---------\n`;
    data.education.forEach((edu, index) => {
      formatted += `${index + 1}. ${edu.school}\n`;
      if (edu.degree) formatted += `   Degree: ${edu.degree}\n`;
      if (edu.field) formatted += `   Field: ${edu.field}\n`;
      if (edu.description) formatted += `   Description: ${edu.description}\n`;
      formatted += `\n`;
    });
  }

  if (data.skills.length > 0) {
    formatted += `SKILLS\n------\n`;
    formatted += data.skills.join(', ') + '\n\n';
  }

  if (data.languages.length > 0) {
    formatted += `LANGUAGES\n---------\n`;
    data.languages.forEach((lang, index) => {
      formatted += `${index + 1}. ${lang.name}`;
      if (lang.proficiency) formatted += ` (${lang.proficiency})`;
      formatted += `\n`;
    });
    formatted += `\n`;
  }

  if (data.certifications.length > 0) {
    formatted += `CERTIFICATIONS\n--------------\n`;
    data.certifications.forEach((cert, index) => {
      formatted += `${index + 1}. ${cert.name}\n`;
      if (cert.issuer) formatted += `   Issuer: ${cert.issuer}\n`;
      if (cert.credentialId) formatted += `   Credential ID: ${cert.credentialId}\n`;
      if (cert.url) formatted += `   URL: ${cert.url}\n`;
      formatted += `\n`;
    });
  }

  return formatted;
}

function mapLinkedInPageStructure() {
  console.log('=== LINKEDIN PAGE STRUCTURE ANALYSIS ===');

  let structureReport = 'LINKEDIN PAGE STRUCTURE ANALYSIS\n';
  structureReport += '=====================================\n\n';

  // 1. Check for main sections
  const sections = document.querySelectorAll('section');
  structureReport += `Total sections found: ${sections.length}\n\n`;

  sections.forEach((section, index) => {
    const sectionText = section.textContent.trim();
    const sectionId = section.id || 'no-id';
    const sectionClasses = section.className || 'no-classes';

    structureReport += `SECTION ${index + 1}:\n`;
    structureReport += `  ID: ${sectionId}\n`;
    structureReport += `  Classes: ${sectionClasses}\n`;
    structureReport += `  Text length: ${sectionText.length} chars\n`;
    structureReport += `  Text preview: "${sectionText.substring(0, 150)}..."\n`;

    // Check for specific elements that might contain about info
    const spans = section.querySelectorAll('span');
    const divs = section.querySelectorAll('div');
    const paragraphs = section.querySelectorAll('p');

    structureReport += `  Contains: ${spans.length} spans, ${divs.length} divs, ${paragraphs.length} paragraphs\n`;

    // Look for substantial text content
    const substantialTexts = [];
    [...spans, ...divs, ...paragraphs].forEach(el => {
      const text = el.textContent.trim();
      if (text.length > 100 && text.length < 1000) {
        substantialTexts.push({
          tag: el.tagName.toLowerCase(),
          length: text.length,
          preview: text.substring(0, 100)
        });
      }
    });

    if (substantialTexts.length > 0) {
      structureReport += `  Substantial text blocks found: ${substantialTexts.length}\n`;
      substantialTexts.forEach((textBlock, i) => {
        structureReport += `    ${i + 1}. <${textBlock.tag}> (${textBlock.length} chars): "${textBlock.preview}..."\n`;
      });
    }

    structureReport += '\n';
  });

  // 2. Check for elements with specific IDs
  const commonIds = ['about', 'experience', 'education', 'skills', 'languages', 'certifications', 'licenses_and_certifications'];
  structureReport += 'ELEMENTS BY COMMON IDS:\n';
  structureReport += '======================\n';

  commonIds.forEach(id => {
    const element = document.querySelector(`#${id}`);
    if (element) {
      const container = element.closest('section');
      structureReport += `#${id}: Found\n`;
      if (container) {
        const containerText = container.textContent.trim();
        structureReport += `  Container text length: ${containerText.length}\n`;
        structureReport += `  Container preview: "${containerText.substring(0, 200)}..."\n`;
      }
    } else {
      structureReport += `#${id}: Not found\n`;
    }
  });

  // 3. Look for text patterns that might be about content
  structureReport += '\nPOTENTIAL ABOUT CONTENT:\n';
  structureReport += '========================\n';

  const allElements = document.querySelectorAll('*');
  const aboutCandidates = [];

  allElements.forEach(el => {
    const text = el.textContent.trim();
    if (text.length > 200 && text.length < 2000) {
      // Check for about-like content
      const aboutKeywords = ['passionate', 'experience', 'background', 'expertise', 'focus', 'specialize', 'work', 'career', 'dedicated', 'committed'];
      const hasAboutKeywords = aboutKeywords.some(keyword => text.toLowerCase().includes(keyword));

      if (hasAboutKeywords) {
        aboutCandidates.push({
          tag: el.tagName.toLowerCase(),
          classes: el.className,
          length: text.length,
          text: text,
          keywords: aboutKeywords.filter(keyword => text.toLowerCase().includes(keyword))
        });
      }
    }
  });

  if (aboutCandidates.length > 0) {
    structureReport += `Found ${aboutCandidates.length} potential about content blocks:\n\n`;
    aboutCandidates.forEach((candidate, i) => {
      structureReport += `CANDIDATE ${i + 1}:\n`;
      structureReport += `  Tag: <${candidate.tag}>\n`;
      structureReport += `  Classes: ${candidate.classes}\n`;
      structureReport += `  Length: ${candidate.length} chars\n`;
      structureReport += `  Keywords found: ${candidate.keywords.join(', ')}\n`;
      structureReport += `  Text: "${candidate.text}"\n\n`;
    });
  } else {
    structureReport += 'No potential about content found based on keywords.\n';
  }

  console.log(structureReport);
  return structureReport;
}

async function findAboutSection() {
  console.log('[content.js] Starting about section search...');

  // Strategy 1: Look for #about anchor
  let aboutSection = document.querySelector('#about');
  console.log('[content.js] #about element:', !!aboutSection);

  if (aboutSection) {
    console.log('[content.js] Found #about anchor');
    const aboutContainer = aboutSection.closest('section');
    console.log('[content.js] About container:', !!aboutContainer);

    if (aboutContainer) {
      console.log('[content.js] About container HTML preview:', aboutContainer.innerHTML.substring(0, 200));
      const aboutText = extractTextFromAboutContainer(aboutContainer);
      if (aboutText) {
        console.log('[content.js] Found about text via #about anchor:', aboutText.substring(0, 100));
        return aboutText;
      } else {
        console.log('[content.js] No text extracted from about container');
      }
    }
  }

  // Strategy 2: Look for sections containing "About" text
  const sections = document.querySelectorAll('section');
  console.log(`[DEBUG] Searching through ${sections.length} sections for about content`);

  for (let i = 0; i < sections.length; i++) {
    const section = sections[i];
    const sectionText = section.textContent.toLowerCase();

    // Check if this section likely contains about information
    if (sectionText.includes('about') && sectionText.length > 100) {
      console.log(`[DEBUG] Section ${i} contains 'about' and has substantial content`);

      // Skip if it contains other profile sections
      if (sectionText.includes('experience') || sectionText.includes('education') || sectionText.includes('skills')) {
        console.log(`[DEBUG] Section ${i} contains other profile sections, skipping`);
        continue;
      }

      const aboutText = extractTextFromAboutContainer(section);
      if (aboutText) {
        console.log('[DEBUG] Found about text via section search:', aboutText.substring(0, 100));
        return aboutText;
      }
    }
  }

  // Strategy 3: Look for text patterns that suggest about content
  console.log('[DEBUG] Trying pattern-based search...');
  const allElements = document.querySelectorAll('div, span, p');

  for (const element of allElements) {
    const text = element.textContent.trim();

    // Look for substantial text that might be about content
    if (text.length > 150 && text.length < 2000) {
      // Check if it contains typical about section keywords
      const aboutKeywords = ['passionate', 'experience', 'background', 'expertise', 'focus', 'specialize', 'work', 'career'];
      const hasAboutKeywords = aboutKeywords.some(keyword => text.toLowerCase().includes(keyword));

      // Make sure it's not part of experience/education sections
      const isNotOtherSection = !text.toLowerCase().includes('company:') &&
                               !text.toLowerCase().includes('duration:') &&
                               !text.toLowerCase().includes('university') &&
                               !element.closest('[id*="experience"]') &&
                               !element.closest('[id*="education"]');

      if (hasAboutKeywords && isNotOtherSection) {
        console.log('[DEBUG] Found potential about text via pattern matching:', text.substring(0, 100));
        return text;
      }
    }
  }

  // Strategy 4: Look for the first substantial text block after the header
  console.log('[DEBUG] Looking for first substantial text block...');
  const mainContent = document.querySelector('main');
  if (mainContent) {
    const textElements = mainContent.querySelectorAll('div, span, p');
    for (const element of textElements) {
      const text = element.textContent.trim();
      if (text.length > 100 && text.length < 1500) {
        // Make sure it's not navigation, header, or other UI elements
        const isContentText = !text.toLowerCase().includes('connect') &&
                             !text.toLowerCase().includes('message') &&
                             !text.toLowerCase().includes('follow') &&
                             !text.toLowerCase().includes('more') &&
                             !element.closest('nav') &&
                             !element.closest('header');

        if (isContentText) {
          console.log('[DEBUG] Found potential about text as first substantial block:', text.substring(0, 100));
          return text;
        }
      }
    }
  }

  console.log('[DEBUG] No about section found with any strategy');
  return '';
}

async function extractTextFromAboutContainer(container) {
  console.log('[content.js] Extracting text from about container...');

  // Enhanced selectors for about content
  const selectors = [
    '.pv-shared-text-with-see-more .inline-show-more-text',
    '.pv-shared-text-with-see-more .visually-hidden',
    '.pv-shared-text-with-see-more span[aria-hidden="true"]',
    '.inline-show-more-text span[aria-hidden="true"]',
    '.pv-shared-text-with-see-more .full-width span',
    '.pv-shared-text-with-see-more',
    '.inline-show-more-text',
    'span[aria-hidden="true"]',
    '.visually-hidden',
    '.pv-about-section span',
    '.pv-oc span[aria-hidden="true"]',
    '.display-flex.full-width span'
  ];

  // Try each selector and collect all potential text
  const textCandidates = [];

  for (const selector of selectors) {
    const elements = container.querySelectorAll(selector);
    console.log(`[content.js] Trying selector "${selector}": found ${elements.length} elements`);

    elements.forEach((element, index) => {
      const text = element.textContent.trim();
      if (text.length > 50 &&
          !text.includes('Show all') &&
          !text.includes('...more') &&
          !text.includes('Show less') &&
          !text.toLowerCase().includes('about') &&
          !text.includes('See more') &&
          !text.includes('See less')) {

        textCandidates.push({
          text: text,
          length: text.length,
          selector: selector,
          element: element
        });
        console.log(`[content.js] Found candidate text (${text.length} chars): "${text.substring(0, 100)}..."`);
      }
    });
  }

  // If we found candidates, return the longest one
  if (textCandidates.length > 0) {
    textCandidates.sort((a, b) => b.length - a.length);
    const bestCandidate = textCandidates[0];
    console.log(`[content.js] Selected best candidate (${bestCandidate.length} chars) from selector: ${bestCandidate.selector}`);
    return bestCandidate.text;
  }

  // Fallback: try to get all text content and filter out UI elements
  console.log('[content.js] No specific selectors worked, trying fallback approach...');
  const allElements = container.querySelectorAll('*');
  const allTextCandidates = [];

  allElements.forEach(element => {
    const text = element.textContent.trim();
    const directText = Array.from(element.childNodes)
      .filter(node => node.nodeType === Node.TEXT_NODE)
      .map(node => node.textContent.trim())
      .join(' ')
      .trim();

    if (directText.length > 50) {
      allTextCandidates.push({
        text: directText,
        length: directText.length,
        element: element
      });
    } else if (text.length > 100 && text.length < 2000) {
      // Check if this element's text is mostly unique (not contained in parent)
      const parent = element.parentElement;
      const isUniqueText = !parent || text.length > parent.textContent.trim().length * 0.8;

      if (isUniqueText) {
        allTextCandidates.push({
          text: text,
          length: text.length,
          element: element
        });
      }
    }
  });

  if (allTextCandidates.length > 0) {
    // Filter out UI text and sort by length
    const filteredCandidates = allTextCandidates.filter(candidate => {
      const text = candidate.text.toLowerCase();
      return !text.includes('show all') &&
             !text.includes('show more') &&
             !text.includes('see more') &&
             !text.includes('connect') &&
             !text.includes('message') &&
             !text.includes('follow') &&
             !text.includes('about') &&
             candidate.length > 80;
    });

    if (filteredCandidates.length > 0) {
      filteredCandidates.sort((a, b) => b.length - a.length);
      const bestCandidate = filteredCandidates[0];
      console.log(`[content.js] Found text via fallback approach (${bestCandidate.length} chars)`);
      return bestCandidate.text;
    }
  }

  console.log('[content.js] No about text found in container');
  return '';
}

function debugPageStructure() {
  console.log('[DEBUG] === LinkedIn Page Structure Debug ===');
  console.log('[DEBUG] Current URL:', window.location.href);
  console.log('[DEBUG] Page title:', document.title);

  // Debug about section
  const aboutSection = document.querySelector('#about');
  console.log('[DEBUG] About section query result:', aboutSection);

  if (aboutSection) {
    console.log('[DEBUG] About section found!');
    const aboutContainer = aboutSection.closest('section');
    if (aboutContainer) {
      console.log('[DEBUG] About container found');
      console.log('[DEBUG] About container HTML length:', aboutContainer.innerHTML.length);

      // Log all spans in about section
      const spans = aboutContainer.querySelectorAll('span');
      console.log('[DEBUG] About section spans count:', spans.length);

      if (spans.length > 0) {
        spans.forEach((span, index) => {
          const text = span.textContent.trim();
          if (text.length > 20) {
            console.log(`[DEBUG] Span ${index} (${text.length} chars): "${text.substring(0, 100)}..."`);
          }
        });
      } else {
        console.log('[DEBUG] No spans found in about container');
      }
    } else {
      console.log('[DEBUG] About container NOT found');
    }
  } else {
    console.log('[DEBUG] About section NOT found - checking all sections...');
    const allSections = document.querySelectorAll('section');
    console.log('[DEBUG] Total sections on page:', allSections.length);

    allSections.forEach((section, index) => {
      const sectionText = section.textContent.toLowerCase();
      if (sectionText.includes('about')) {
        console.log(`[DEBUG] Section ${index} contains "about":`, sectionText.substring(0, 200));
      }
    });
  }

  // Debug location elements
  console.log('[DEBUG] Looking for location elements...');
  const locationSelectors = [
    '.pv-text-details__left-panel .text-body-small',
    '.pv-top-card--list .pv-top-card--list-bullet',
    '.text-body-small'
  ];

  locationSelectors.forEach(selector => {
    const elements = document.querySelectorAll(selector);
    console.log(`[DEBUG] Selector "${selector}" found ${elements.length} elements`);
    elements.forEach((el, index) => {
      if (index < 3) { // Only log first 3
        console.log(`[DEBUG] Element ${index}: "${el.textContent.trim()}"`);
      }
    });
  });

  // Debug headline elements
  console.log('[DEBUG] Looking for headline elements...');
  const headlineSelectors = [
    'div.text-body-medium.break-words',
    '.pv-text-details__left-panel .text-body-medium'
  ];

  headlineSelectors.forEach(selector => {
    const elements = document.querySelectorAll(selector);
    console.log(`[DEBUG] Headline selector "${selector}" found ${elements.length} elements`);
    elements.forEach((el, index) => {
      if (index < 3) {
        console.log(`[DEBUG] Headline ${index}: "${el.textContent.trim()}"`);
      }
    });
  });
}

/** Extract first name from full name */
function extractFirstName(fullName) {
  if (!fullName) return '';

  // Split by spaces and take the first word
  const words = fullName.trim().split(/\s+/);
  if (words.length === 0) return '';

  // Remove any non-alphabetic characters from the first word
  const firstName = words[0].replace(/[^a-zA-Z]/g, '');

  return firstName;
}

/** Get the About section from LinkedIn profile */
function getProfileAbout() {
  // Try multiple selectors for the About section
  const aboutSelectors = [
    '[data-generated-suggestion-target="about"] .pv-shared-text-with-see-more .visually-hidden',
    '[data-generated-suggestion-target="about"] .pv-shared-text-with-see-more .inline-show-more-text',
    '[data-generated-suggestion-target="about"] .pv-shared-text-with-see-more span[aria-hidden="true"]',
    '.pv-about-section .pv-about__summary-text .lt-line-clamp__raw-line',
    '.pv-about-section .pv-shared-text-with-see-more .visually-hidden',
    '.pv-about-section .pv-shared-text-with-see-more span[aria-hidden="true"]',
    '.about-section .pv-about__summary-text',
    '.about-section .inline-show-more-text',
    // Fallback selectors
    '.pv-about__summary-text',
    '.about-section-text'
  ];

  for (const selector of aboutSelectors) {
    const element = document.querySelector(selector);
    if (element && element.textContent.trim()) {
      const text = element.textContent.trim();
      // Filter out common non-about text
      if (text.length > 20 &&
          !text.includes('Show more') &&
          !text.includes('Show less') &&
          !text.includes('see more')) {
        console.log('[content.js] Found about section with selector:', selector);
        return text;
      }
    }
  }

  console.log('[content.js] Could not find about section');
  return '';
}

/** Generate outreach message template */
function generateOutreachMessage(firstName) {
  const messageType = extensionConfig?.settings?.outreachMessage || "template_version_1";

  if (messageType === "template_version_2") {
    // Jai's version
    return `Hi ${firstName},

I work at Blue Wire Capital, a VC fund in London (previous investments include Monzo, Deliveroo, and beehiiv).`;
  } else {
    // Hanna's version (template_version_1)
    return `Hey ${firstName} - would love to learn more about what you're building. I'm an early-stage, global investor with Blue Wire - we're a very founder-focussed fund (first checks into Monzo & Deliveroo) & like that `;
  }
}

/** Copy outreach template to clipboard */
async function copyOutreachTemplate() {
  console.log('[content.js] Generating outreach template...');

  // Get the profile name
  const fullName = getProfileName();
  console.log('[content.js] Full name extracted:', fullName);

  // Extract first name
  const firstName = extractFirstName(fullName);
  console.log('[content.js] First name extracted:', firstName);

  if (!firstName) {
    throw new Error('Could not extract first name from profile');
  }

  // Generate the outreach message
  const message = generateOutreachMessage(firstName);
  console.log('[content.js] Generated message:', message);

  // Copy to clipboard
  try {
    await navigator.clipboard.writeText(message);
    console.log('[content.js] Outreach template copied to clipboard');
  } catch (error) {
    console.error('[content.js] Failed to copy to clipboard:', error);
    // Fallback method
    const textArea = document.createElement('textarea');
    textArea.value = message;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
    console.log('[content.js] Outreach template copied to clipboard using fallback method');
  }
}

/** Show talent bank popup */
async function showTalentBankPopup() {
  console.log('[content.js] Showing talent bank popup...');

  // Get profile data
  const name = getProfileName();
  const about = getProfileAbout();
  const profileUrl = window.location.href;

  if (!name) {
    throw new Error('Could not get profile name');
  }

  // Create popup overlay
  const overlay = document.createElement('div');
  overlay.className = 'bwc-talent-bank-popup-overlay';
  overlay.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
  `;

  // Create popup container
  const popup = document.createElement('div');
  popup.style.cssText = `
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    width: 600px;
    max-width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
  `;

  // Create header
  const header = document.createElement('div');
  header.style.cssText = `
    padding: 16px 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  `;

  const title = document.createElement('h2');
  title.textContent = 'Add to Talent Bank';
  title.style.cssText = `
    font-size: 18px;
    font-weight: 600;
    margin: 0;
  `;

  const closeBtn = document.createElement('button');
  closeBtn.innerHTML = '&times;';
  closeBtn.style.cssText = `
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #666;
  `;
  closeBtn.addEventListener('click', () => overlay.remove());

  header.appendChild(title);
  header.appendChild(closeBtn);

  // Create content area
  const content = document.createElement('div');
  content.style.cssText = `
    padding: 20px;
  `;

  // Add form content
  content.innerHTML = createTalentBankForm(name, about, profileUrl);

  popup.appendChild(header);
  popup.appendChild(content);
  overlay.appendChild(popup);
  document.body.appendChild(overlay);

  // Close popup when clicking outside
  overlay.addEventListener('click', (e) => {
    if (e.target === overlay) {
      overlay.remove();
    }
  });

  // Close popup with Escape key
  const handleEscapeKey = (e) => {
    if (e.key === 'Escape') {
      overlay.remove();
      document.removeEventListener('keydown', handleEscapeKey);
    }
  };
  document.addEventListener('keydown', handleEscapeKey);

  // Set up form handlers
  setupTalentBankFormHandlers(content, overlay);
}

/** Create talent bank form HTML */
function createTalentBankForm(name, about, profileUrl) {
  // Get today's date in YYYY-MM-DD format
  const today = new Date().toISOString().split('T')[0];

  return `
    <div style="margin-bottom: 20px;">
      <h3 style="margin: 0 0 8px 0; color: #191919; font-size: 16px; font-weight: 600;">Profile Information</h3>
      <p style="margin: 0 0 4px 0; color: #666;"><strong>Name:</strong> ${name}</p>
      <p style="margin: 0 0 4px 0; color: #666;"><strong>Profile URL:</strong> <a href="${profileUrl}" target="_blank" style="color: #0a66c2;">${profileUrl}</a></p>
      <p style="margin: 0 0 8px 0; color: #666;"><strong>About:</strong> ${about || 'No about section found'}</p>
    </div>

    <div style="margin-bottom: 20px;">
      <label style="font-weight: 600; display: block; color: #191919; margin-bottom: 8px;">
        Functions <span style="color: red;">*</span>
      </label>
      <div id="functions-checkboxes" style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
        <label style="display: flex; align-items: center; cursor: pointer;">
          <input type="checkbox" name="functions" value="Technical" style="margin-right: 8px;">
          Technical
        </label>
        <label style="display: flex; align-items: center; cursor: pointer;">
          <input type="checkbox" name="functions" value="Product" style="margin-right: 8px;">
          Product
        </label>
        <label style="display: flex; align-items: center; cursor: pointer;">
          <input type="checkbox" name="functions" value="Sales/Commercial" style="margin-right: 8px;">
          Sales/Commercial
        </label>
        <label style="display: flex; align-items: center; cursor: pointer;">
          <input type="checkbox" name="functions" value="Marketing" style="margin-right: 8px;">
          Marketing
        </label>
        <label style="display: flex; align-items: center; cursor: pointer;">
          <input type="checkbox" name="functions" value="Operations" style="margin-right: 8px;">
          Operations
        </label>
        <label style="display: flex; align-items: center; cursor: pointer;">
          <input type="checkbox" name="functions" value="Finance" style="margin-right: 8px;">
          Finance
        </label>
        <label style="display: flex; align-items: center; cursor: pointer;">
          <input type="checkbox" name="functions" value="People/HR" style="margin-right: 8px;">
          People/HR
        </label>
        <label style="display: flex; align-items: center; cursor: pointer;">
          <input type="checkbox" name="functions" value="Legal/Compliance" style="margin-right: 8px;">
          Legal/Compliance
        </label>
      </div>
    </div>

    <div style="margin-bottom: 20px;">
      <label style="font-weight: 600; display: block; color: #191919; margin-bottom: 8px;">
        Purpose <span style="color: red;">*</span>
      </label>
      <div id="purpose-checkboxes" style="display: flex; flex-direction: column; gap: 8px;">
        <label style="display: flex; align-items: center; cursor: pointer;">
          <input type="checkbox" name="purpose" value="Potential hire" style="margin-right: 8px;">
          Potential hire
        </label>
        <label style="display: flex; align-items: center; cursor: pointer;">
          <input type="checkbox" name="purpose" value="Potential future founder" style="margin-right: 8px;">
          Potential future founder
        </label>
        <label style="display: flex; align-items: center; cursor: pointer;">
          <input type="checkbox" name="purpose" value="Useful for info" style="margin-right: 8px;">
          Useful for info
        </label>
      </div>
    </div>

    <div style="margin-bottom: 20px;">
      <label for="reach-out-date" style="font-weight: 600; display: block; color: #191919; margin-bottom: 8px;">
        When to reach out again (optional)
      </label>
      <input
        type="date"
        id="reach-out-date"
        value="${today}"
        style="
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #e0e0e0;
          border-radius: 4px;
          box-sizing: border-box;
          font-size: 14px;
          color: #191919;
          background-color: #fff;
          box-shadow: 0 1px 3px rgba(0,0,0,0.08);
        "
      >
    </div>

    <div style="margin-bottom: 20px;">
      <label for="portcos-interest" style="font-weight: 600; display: block; color: #191919; margin-bottom: 8px;">
        Specific portcos they might be of interest to (optional)
      </label>
      <input
        type="text"
        id="portcos-interest"
        placeholder="Enter portfolio companies..."
        style="
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #e0e0e0;
          border-radius: 4px;
          box-sizing: border-box;
          font-size: 14px;
          color: #191919;
          background-color: #fff;
          box-shadow: 0 1px 3px rgba(0,0,0,0.08);
        "
      >
    </div>

    <div style="display: flex; gap: 12px; justify-content: flex-end;">
      <button
        id="talent-bank-cancel"
        style="
          padding: 8px 16px;
          background-color: #fff;
          color: #666;
          border: 1px solid #e0e0e0;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
          font-weight: 500;
        "
      >
        Cancel
      </button>
      <button
        id="talent-bank-submit"
        style="
          padding: 8px 16px;
          background-color: #0a66c2;
          color: #fff;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
          font-weight: 600;
        "
      >
        Add to Talent Bank
      </button>
    </div>
  `;
}

/** Setup talent bank form handlers */
function setupTalentBankFormHandlers(content, overlay) {
  const cancelBtn = content.querySelector('#talent-bank-cancel');
  const submitBtn = content.querySelector('#talent-bank-submit');

  // Cancel button
  cancelBtn.addEventListener('click', () => {
    overlay.remove();
  });

  // Submit button
  submitBtn.addEventListener('click', async () => {
    try {
      // Validate required fields
      const functionsChecked = content.querySelectorAll('input[name="functions"]:checked');
      const purposeChecked = content.querySelectorAll('input[name="purpose"]:checked');

      if (functionsChecked.length === 0) {
        alert('Please select at least one function.');
        return;
      }

      if (purposeChecked.length === 0) {
        alert('Please select at least one purpose.');
        return;
      }

      // Disable submit button and show loading
      submitBtn.disabled = true;
      submitBtn.textContent = 'Adding...';

      // Collect form data
      const formData = {
        name: getProfileName(),
        about: getProfileAbout(),
        profileUrl: window.location.href,
        functions: Array.from(functionsChecked).map(cb => cb.value),
        purpose: Array.from(purposeChecked).map(cb => cb.value),
        reachOutDate: content.querySelector('#reach-out-date').value || null,
        portcosInterest: content.querySelector('#portcos-interest').value.trim() || null,
        bwcContact: extensionConfig?.settings?.bwcContactName || 'Unknown'
      };

      console.log('[content.js] Submitting talent bank data:', formData);

      // Send to background script
      const response = await new Promise(resolve =>
        chrome.runtime.sendMessage(
          { type: 'addToTalentBank', data: formData },
          resolve
        )
      );

      if (response && response.success) {
        submitBtn.textContent = 'Added ✅';
        setTimeout(() => {
          overlay.remove();
        }, 1500);
      } else {
        throw new Error(response?.error || 'Failed to add to talent bank');
      }

    } catch (error) {
      console.error('[content.js] Error submitting to talent bank:', error);
      submitBtn.textContent = 'Error ❌';
      alert('Error adding to talent bank: ' + error.message);

      // Reset button
      setTimeout(() => {
        submitBtn.disabled = false;
        submitBtn.textContent = 'Add to Talent Bank';
      }, 2000);
    }
  });
}

/** 4) Main runner */
async function run() {
  const path = window.location.pathname;
  if (!/^\/(in|company)\//.test(path)) {
    removeAdminPanel();
    return;
  }

  // Load config if not already loaded
  if (!extensionConfig) {
    await loadConfig();
  }

  const name = getProfileName();
  if (!name) {
    removeAdminPanel();
    console.warn('[content.js] no profile name');
    return;
  }

  const response = await searchAffinityByName(name);
  insertAdminPanel(response);
}

/** 5) Poll + SPA handling */
function waitForMainSection() {
  let attempts = 0;
  const id = setInterval(() => {
    attempts++;
    if (document.querySelector('main section')) {
      clearInterval(id);
      run();
    } else if (attempts > 20) {
      clearInterval(id);
      removeAdminPanel();
      console.warn('[content.js] timed out waiting for main section');
    }
  }, 100);
}
waitForMainSection();

let lastUrl = location.href;
new MutationObserver(() => {
  if (location.href !== lastUrl) {
    lastUrl = location.href;
    waitForMainSection();
  }
}).observe(document, { subtree: true, childList: true });
